import { Client, Databases, ID } from 'node-appwrite';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Initialize Appwrite client
const client = new Client()
  .setEndpoint(
    process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT ||
      'https://fra.cloud.appwrite.io/v1'
  )
  .setProject(
    process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID || '6874acbf001bf118e099'
  )
  .setKey(process.env.APPWRITE_API_KEY || 'your_api_key_here');

const databases = new Databases(client);

const DATABASE_ID = process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID;
const COLLECTIONS = {
  STORE_INFO: process.env.NEXT_PUBLIC_APPWRITE_STORE_INFO_COLLECTION_ID,
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_CATEGORIES_COLLECTION_ID,
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_PRODUCTS_COLLECTION_ID,
  TESTIMONIALS: process.env.NEXT_PUBLIC_APPWRITE_TESTIMONIALS_COLLECTION_ID,
};

async function seedStoreInfo() {
  try {
    const storeInfo = {
      name: 'Salis Touch',
      description:
        'Premium handcrafted products and personalized services for discerning customers.',
      phone: '******-0123',
      email: '<EMAIL>',
      address: JSON.stringify({
        street: '123 Main Street',
        city: 'Anytown',
        state: 'CA',
        zipCode: '12345',
        country: 'United States',
      }),
      hours: JSON.stringify([
        {
          dayOfWeek: 'monday',
          openTime: '09:00',
          closeTime: '18:00',
          isClosed: false,
        },
        {
          dayOfWeek: 'tuesday',
          openTime: '09:00',
          closeTime: '18:00',
          isClosed: false,
        },
        {
          dayOfWeek: 'wednesday',
          openTime: '09:00',
          closeTime: '18:00',
          isClosed: false,
        },
        {
          dayOfWeek: 'thursday',
          openTime: '09:00',
          closeTime: '18:00',
          isClosed: false,
        },
        {
          dayOfWeek: 'friday',
          openTime: '09:00',
          closeTime: '18:00',
          isClosed: false,
        },
        {
          dayOfWeek: 'saturday',
          openTime: '10:00',
          closeTime: '17:00',
          isClosed: false,
        },
        { dayOfWeek: 'sunday', openTime: '', closeTime: '', isClosed: true },
      ]),
      aboutUs:
        'We are passionate about bringing you exceptional products that enhance your lifestyle.',
      mission:
        'To provide premium quality products with exceptional customer service.',
      vision:
        'To be the leading destination for discerning customers seeking quality and craftsmanship.',
    };

    const result = await databases.createDocument(
      DATABASE_ID,
      COLLECTIONS.STORE_INFO,
      ID.unique(),
      storeInfo
    );
    console.log('✓ Store info created:', result.$id);
  } catch (error) {
    console.error('Error creating store info:', error);
  }
}

async function seedCategories() {
  const categories = [
    {
      name: 'Home & Living',
      description: 'Beautiful items for your home',
      slug: 'home-living',
      sortOrder: 1,
      isActive: true,
    },
    {
      name: 'Fashion & Accessories',
      description: 'Stylish fashion items and accessories',
      slug: 'fashion-accessories',
      sortOrder: 2,
      isActive: true,
    },
    {
      name: 'Art & Crafts',
      description: 'Handmade art and craft items',
      slug: 'art-crafts',
      sortOrder: 3,
      isActive: true,
    },
  ];

  for (const category of categories) {
    try {
      const result = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.CATEGORIES,
        ID.unique(),
        category
      );
      console.log('✓ Category created:', result.name);
    } catch (error) {
      console.error('Error creating category:', error);
    }
  }
}

async function seedProducts() {
  // First get categories to use their IDs
  const categoriesResponse = await databases.listDocuments(
    DATABASE_ID,
    COLLECTIONS.CATEGORIES
  );
  const homeCategory = categoriesResponse.documents.find(
    (cat) => cat.slug === 'home-living'
  );
  const fashionCategory = categoriesResponse.documents.find(
    (cat) => cat.slug === 'fashion-accessories'
  );
  const artCategory = categoriesResponse.documents.find(
    (cat) => cat.slug === 'art-crafts'
  );

  const products = [
    {
      name: 'Handcrafted Ceramic Vase',
      description:
        'Beautiful handcrafted ceramic vase perfect for any home decor. Made with premium materials and attention to detail. This elegant piece features a modern design that complements both contemporary and traditional interiors.',
      shortDescription: 'Elegant ceramic vase for modern homes',
      price: 89.99,
      compareAtPrice: 120.0,
      sku: 'CV-001',
      category: 'Home & Living',
      categoryId: homeCategory?.$id || 'home-living',
      images: [],
      specifications: JSON.stringify([
        { name: 'Material', value: 'Premium Ceramic' },
        { name: 'Height', value: '12 inches' },
        { name: 'Diameter', value: '6 inches' },
        { name: 'Weight', value: '2.5 lbs' },
      ]),
      features: [
        'Handcrafted',
        'Premium Ceramic',
        'Modern Design',
        'Dishwasher Safe',
      ],
      inStock: true,
      stockQuantity: 15,
      featured: true,
      tags: ['ceramic', 'vase', 'home-decor', 'handmade'],
      weight: 2.5,
      dimensions: JSON.stringify({
        height: 12,
        width: 6,
        depth: 6,
        unit: 'inches',
      }),
      materials: ['Ceramic', 'Glaze'],
      careInstructions:
        'Hand wash recommended. Dishwasher safe on gentle cycle.',
      slug: 'handcrafted-ceramic-vase',
    },
    {
      name: 'Artisan Leather Handbag',
      description:
        'Luxurious handcrafted leather handbag made from premium full-grain leather. Features multiple compartments and a timeless design that never goes out of style.',
      shortDescription: 'Premium leather handbag with timeless design',
      price: 249.99,
      compareAtPrice: 320.0,
      sku: 'LH-002',
      category: 'Fashion & Accessories',
      categoryId: fashionCategory?.$id || 'fashion-accessories',
      images: [],
      specifications: JSON.stringify([
        { name: 'Material', value: 'Full-Grain Leather' },
        { name: 'Dimensions', value: '14" x 10" x 5"' },
        { name: 'Strap Drop', value: '8 inches' },
        { name: 'Closure', value: 'Magnetic Snap' },
      ]),
      features: [
        'Full-Grain Leather',
        'Multiple Compartments',
        'Adjustable Strap',
        'Handcrafted',
      ],
      inStock: true,
      stockQuantity: 8,
      featured: true,
      tags: ['leather', 'handbag', 'fashion', 'luxury'],
      weight: 1.8,
      dimensions: JSON.stringify({
        height: 10,
        width: 14,
        depth: 5,
        unit: 'inches',
      }),
      materials: ['Full-Grain Leather', 'Cotton Lining', 'Brass Hardware'],
      careInstructions: 'Clean with leather conditioner. Avoid water exposure.',
      slug: 'artisan-leather-handbag',
    },
    {
      name: 'Hand-Painted Canvas Art',
      description:
        'Original hand-painted canvas artwork featuring abstract designs. Each piece is unique and signed by the artist. Perfect for adding a personal touch to any space.',
      shortDescription: 'Original hand-painted abstract canvas art',
      price: 175.0,
      compareAtPrice: 225.0,
      sku: 'CA-003',
      category: 'Art & Crafts',
      categoryId: artCategory?.$id || 'art-crafts',
      images: [],
      specifications: JSON.stringify([
        { name: 'Medium', value: 'Acrylic on Canvas' },
        { name: 'Size', value: '16" x 20"' },
        { name: 'Frame', value: 'Not Included' },
        { name: 'Orientation', value: 'Portrait or Landscape' },
      ]),
      features: [
        'Hand-Painted',
        'Original Artwork',
        'Signed by Artist',
        'Ready to Hang',
      ],
      inStock: true,
      stockQuantity: 5,
      featured: false,
      tags: ['art', 'painting', 'canvas', 'abstract', 'original'],
      weight: 1.2,
      dimensions: JSON.stringify({
        height: 20,
        width: 16,
        depth: 1,
        unit: 'inches',
      }),
      materials: ['Canvas', 'Acrylic Paint', 'Wood Frame'],
      careInstructions: 'Dust gently with soft cloth. Avoid direct sunlight.',
      slug: 'hand-painted-canvas-art',
    },
  ];

  for (const product of products) {
    try {
      const result = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.PRODUCTS,
        ID.unique(),
        product
      );
      console.log('✓ Product created:', result.name);
    } catch (error) {
      console.error('Error creating product:', error);
    }
  }
}

async function seedTestimonials() {
  const testimonials = [
    {
      customerName: 'Sarah Johnson',
      customerEmail: '<EMAIL>',
      rating: 5,
      title: 'Absolutely Beautiful!',
      content:
        'The ceramic vase I ordered exceeded my expectations. The craftsmanship is incredible and it looks perfect in my living room. Highly recommend!',
      isApproved: true,
      isFeatured: true,
      location: 'San Francisco, CA',
    },
    {
      customerName: 'Michael Chen',
      customerEmail: '<EMAIL>',
      rating: 5,
      title: 'Outstanding Quality',
      content:
        'The leather handbag is exactly what I was looking for. The quality is exceptional and the design is timeless. My wife loves it!',
      isApproved: true,
      isFeatured: true,
      location: 'New York, NY',
    },
    {
      customerName: 'Emily Rodriguez',
      customerEmail: '<EMAIL>',
      rating: 4,
      title: 'Great Customer Service',
      content:
        'Not only are the products beautiful, but the customer service is top-notch. They helped me choose the perfect piece for my home.',
      isApproved: true,
      isFeatured: false,
      location: 'Austin, TX',
    },
  ];

  for (const testimonial of testimonials) {
    try {
      const result = await databases.createDocument(
        DATABASE_ID,
        COLLECTIONS.TESTIMONIALS,
        ID.unique(),
        testimonial
      );
      console.log('✓ Testimonial created:', result.customerName);
    } catch (error) {
      console.error('Error creating testimonial:', error);
    }
  }
}

async function main() {
  console.log('🌱 Starting data seeding...');

  try {
    await seedStoreInfo();
    await seedCategories();
    await seedProducts();
    await seedTestimonials();

    console.log('✅ Data seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
  }
}

main();
