# Appwrite Backend Setup Guide for Salis Touch

This comprehensive guide will walk you through setting up the Appwrite backend for the Salis Touch showcase website. Follow these steps carefully to ensure proper configuration.

## Prerequisites

- An Appwrite Cloud account (<https://cloud.appwrite.io>) or self-hosted Appwrite instance
- Basic understanding of databases and API concepts
- Access to the Salis Touch Next.js project

---

## 1. Appwrite Project Setup

### 1.1 Create a New Appwrite Project

**For Appwrite Cloud:**

1. Go to <https://cloud.appwrite.io> and sign in or create an account
2. Click **"Create Project"** button on the dashboard
3. Enter project details:
   - **Project Name**: `Salis Touch`
   - **Project ID**: `salis-touch` (or auto-generated)
   - **Region**: Choose the region closest to your users
4. Click **"Create"** to create the project

**For Self-Hosted Appwrite:**

1. Access your Appwrite console (typically `http://localhost/console`)
2. Follow the same steps as above to create a new project

### 1.2 Configure Project Settings

1. Navigate to **Settings** → **General** in your project
2. Configure the following:
   - **Project Name**: Ensure it's set to "Salis Touch"
   - **Project Description**: "Showcase website backend for Salis Touch store"
   - **Project Logo**: Upload your store logo (optional)

### 1.3 Obtain API Keys and Project Information

1. Go to **Settings** → **API Keys**
2. Note down the following values (you'll need these for environment variables):
   - **Project ID**: Found at the top of the page
   - **API Endpoint**: Usually `https://cloud.appwrite.io/v1` for cloud
3. Create a new API Key:
   - Click **"Create API Key"**
   - **Name**: `Salis Touch Server Key`
   - **Expiration**: Set to "Never" or a long duration
   - **Scopes**: Select all scopes for full access
   - Copy and securely store the generated API key

---

## 2. Database Schema Configuration

### 2.1 Create the Database

1. Navigate to **Databases** in the left sidebar
2. Click **"Create Database"**
3. **Database ID**: `salis-touch-db`
4. **Database Name**: `Salis Touch Database`
5. Click **"Create"**

### 2.2 Create Collections

Now create each collection with the following specifications:

#### Collection 1: Products

**Basic Settings:**

- **Collection ID**: `products`
- **Collection Name**: `Products`

**Attributes:**

```text
1. name (String)
   - Size: 200
   - Required: Yes
   - Array: No

2. description (String)
   - Size: 5000
   - Required: Yes
   - Array: No

3. shortDescription (String)
   - Size: 300
   - Required: No
   - Array: No

4. price (Float)
   - Required: Yes
   - Min: 0
   - Max: 999999

5. compareAtPrice (Float)
   - Required: No
   - Min: 0
   - Max: 999999

6. sku (String)
   - Size: 50
   - Required: No
   - Array: No

7. category (String)
   - Size: 100
   - Required: Yes
   - Array: No

8. categoryId (String)
   - Size: 50
   - Required: Yes
   - Array: No

9. images (String)
   - Size: 50
   - Required: No
   - Array: Yes

10. specifications (String)
    - Size: 2000
    - Required: No
    - Array: No
    - Note: Store as JSON string

11. features (String)
    - Size: 100
    - Required: No
    - Array: Yes

12. inStock (Boolean)
    - Required: Yes
    - Default: true

13. stockQuantity (Integer)
    - Required: No
    - Min: 0
    - Max: 999999

14. featured (Boolean)
    - Required: Yes
    - Default: false

15. tags (String)
    - Size: 50
    - Required: No
    - Array: Yes

16. weight (Float)
    - Required: No
    - Min: 0

17. dimensions (String)
    - Size: 500
    - Required: No
    - Array: No
    - Note: Store as JSON string

18. materials (String)
    - Size: 100
    - Required: No
    - Array: Yes

19. careInstructions (String)
    - Size: 1000
    - Required: No
    - Array: No

20. seoTitle (String)
    - Size: 60
    - Required: No
    - Array: No

21. seoDescription (String)
    - Size: 160
    - Required: No
    - Array: No

22. slug (String)
    - Size: 200
    - Required: Yes
    - Array: No
```

**Indexes:**

1. **slug_index**:
   - Type: Unique
   - Attributes: slug
2. **category_index**:
   - Type: Key
   - Attributes: categoryId
3. **featured_index**:
   - Type: Key
   - Attributes: featured
4. **inStock_index**:
   - Type: Key
   - Attributes: inStock

#### Collection 2: Categories

**Basic Settings:**

- **Collection ID**: `categories`
- **Collection Name**: `Categories`

**Attributes:**

```text
1. name (String)
   - Size: 100
   - Required: Yes
   - Array: No

2. description (String)
   - Size: 500
   - Required: No
   - Array: No

3. slug (String)
   - Size: 100
   - Required: Yes
   - Array: No

4. image (String)
   - Size: 50
   - Required: No
   - Array: No

5. parentId (String)
   - Size: 50
   - Required: No
   - Array: No

6. sortOrder (Integer)
   - Required: Yes
   - Default: 0
   - Min: 0

7. isActive (Boolean)
   - Required: Yes
   - Default: true

8. seoTitle (String)
   - Size: 60
   - Required: No
   - Array: No

9. seoDescription (String)
   - Size: 160
   - Required: No
   - Array: No
```

**Indexes:**

1. **slug_index**:
   - Type: Unique
   - Attributes: slug
2. **active_sort_index**:
   - Type: Key
   - Attributes: isActive, sortOrder

#### Collection 3: Store Info

**Basic Settings:**

- **Collection ID**: `store-info`
- **Collection Name**: `Store Information`

**Attributes:**

```text
1. name (String)
   - Size: 100
   - Required: Yes
   - Array: No

2. description (String)
   - Size: 1000
   - Required: Yes
   - Array: No

3. phone (String)
   - Size: 20
   - Required: Yes
   - Array: No

4. email (String)
   - Size: 100
   - Required: Yes
   - Array: No

5. address (String)
   - Size: 1000
   - Required: Yes
   - Array: No
   - Note: Store as JSON string

6. hours (String)
   - Size: 2000
   - Required: Yes
   - Array: No
   - Note: Store as JSON string

7. socialMedia (String)
   - Size: 1000
   - Required: No
   - Array: No
   - Note: Store as JSON string

8. announcements (String)
   - Size: 2000
   - Required: No
   - Array: No
   - Note: Store as JSON string

9. policies (String)
   - Size: 5000
   - Required: No
   - Array: No
   - Note: Store as JSON string

10. aboutUs (String)
    - Size: 2000
    - Required: No
    - Array: No

11. mission (String)
    - Size: 500
    - Required: No
    - Array: No

12. vision (String)
    - Size: 500
    - Required: No
    - Array: No

13. teamMembers (String)
    - Size: 3000
    - Required: No
    - Array: No
    - Note: Store as JSON string
```

#### Collection 4: Testimonials

**Basic Settings:**

- **Collection ID**: `testimonials`
- **Collection Name**: `Testimonials`

**Attributes:**

```text
1. customerName (String)
   - Size: 100
   - Required: Yes
   - Array: No

2. customerEmail (String)
   - Size: 100
   - Required: No
   - Array: No

3. customerImage (String)
   - Size: 50
   - Required: No
   - Array: No

4. rating (Integer)
   - Required: Yes
   - Min: 1
   - Max: 5

5. title (String)
   - Size: 200
   - Required: No
   - Array: No

6. content (String)
   - Size: 1000
   - Required: Yes
   - Array: No

7. productId (String)
   - Size: 50
   - Required: No
   - Array: No

8. isApproved (Boolean)
   - Required: Yes
   - Default: false

9. isFeatured (Boolean)
   - Required: Yes
   - Default: false

10. location (String)
    - Size: 100
    - Required: No
    - Array: No

11. purchaseDate (String)
    - Size: 20
    - Required: No
    - Array: No
```

**Indexes:**

1. **approved_index**:
   - Type: Key
   - Attributes: isApproved
2. **featured_index**:
   - Type: Key
   - Attributes: isFeatured

#### Collection 5: Inquiries

**Basic Settings:**

- **Collection ID**: `inquiries`
- **Collection Name**: `Customer Inquiries`

**Attributes:**

```text
1. name (String)
   - Size: 100
   - Required: Yes
   - Array: No

2. email (String)
   - Size: 100
   - Required: Yes
   - Array: No

3. phone (String)
   - Size: 20
   - Required: No
   - Array: No

4. subject (String)
   - Size: 200
   - Required: Yes
   - Array: No

5. message (String)
   - Size: 1000
   - Required: Yes
   - Array: No

6. type (String)
   - Size: 20
   - Required: Yes
   - Array: No

7. productId (String)
   - Size: 50
   - Required: No
   - Array: No

8. status (String)
   - Size: 20
   - Required: Yes
   - Default: "new"

9. priority (String)
   - Size: 10
   - Required: Yes
   - Default: "medium"

10. assignedTo (String)
    - Size: 50
    - Required: No
    - Array: No

11. notes (String)
    - Size: 2000
    - Required: No
    - Array: No
    - Note: Store as JSON string

12. source (String)
    - Size: 20
    - Required: Yes
    - Default: "website"
```

**Indexes:**

1. **status_index**:
   - Type: Key
   - Attributes: status
2. **type_index**:
   - Type: Key
   - Attributes: type

#### Collection 6: Analytics

**Basic Settings:**

- **Collection ID**: `analytics`
- **Collection Name**: `Analytics Events`

**Attributes:**

```text
1. type (String)
   - Size: 50
   - Required: Yes
   - Array: No

2. page (String)
   - Size: 200
   - Required: No
   - Array: No

3. productId (String)
   - Size: 50
   - Required: No
   - Array: No

4. userAgent (String)
   - Size: 500
   - Required: No
   - Array: No

5. ipAddress (String)
   - Size: 50
   - Required: No
   - Array: No

6. referrer (String)
   - Size: 200
   - Required: No
   - Array: No

7. sessionId (String)
   - Size: 100
   - Required: No
   - Array: No

8. metadata (String)
   - Size: 1000
   - Required: No
   - Array: No
   - Note: Store as JSON string
```

**Indexes:**

1. **type_date_index**:
   - Type: Key
   - Attributes: type, $createdAt

---

## 3. Storage Configuration

### 3.1 Create Storage Bucket

1. Navigate to **Storage** in the left sidebar
2. Click **"Create Bucket"**
3. Configure the bucket:
   - **Bucket ID**: `product-images`
   - **Bucket Name**: `Product Images`
   - **File Security**: Enabled
   - **Maximum File Size**: `5MB` (5242880 bytes)
   - **Allowed File Extensions**: `jpg,jpeg,png,webp,gif`
   - **Compression**: `gzip` (if available)
   - **Encryption**: Enabled (if available)
   - **Antivirus**: Enabled (if available)

### 3.2 Configure File Permissions

1. Go to **Settings** tab in the bucket
2. Set **Permissions**:
   - **Read Access**: `any` (public read access)
   - **Write Access**: `users` (authenticated users only)
   - **Delete Access**: `users` (authenticated users only)

---

## 4. Security & Permissions

### 4.1 Collection Permissions

For each collection, configure the following permissions:

#### Products Collection

- **Read**: `any` (public read access)
- **Create**: `users` (admin only in practice)
- **Update**: `users` (admin only in practice)
- **Delete**: `users` (admin only in practice)

#### Categories Collection

- **Read**: `any` (public read access)
- **Create**: `users` (admin only)
- **Update**: `users` (admin only)
- **Delete**: `users` (admin only)

#### Store Info Collection

- **Read**: `any` (public read access)
- **Create**: `users` (admin only)
- **Update**: `users` (admin only)
- **Delete**: `users` (admin only)

#### Testimonials Collection

- **Read**: `any` (public read access for approved testimonials)
- **Create**: `any` (allow public submissions)
- **Update**: `users` (admin only)
- **Delete**: `users` (admin only)

#### Inquiries Collection

- **Read**: `users` (admin only)
- **Create**: `any` (allow public submissions)
- **Update**: `users` (admin only)
- **Delete**: `users` (admin only)

#### Analytics Collection

- **Read**: `users` (admin only)
- **Create**: `any` (allow public tracking)
- **Update**: `users` (admin only)
- **Delete**: `users` (admin only)

### 4.2 API Key Configuration

1. Go to **Settings** → **API Keys**
2. Ensure your API key has the following scopes:
   - `databases.read`
   - `databases.write`
   - `files.read`
   - `files.write`
   - `users.read`
   - `users.write`

### 4.3 CORS Settings

1. Navigate to **Settings** → **Domains**
2. Add your domains:
   - **Development**: `http://localhost:3000`
   - **Production**: `https://yourdomain.com`
3. For each domain, ensure all methods are allowed:
   - GET, POST, PUT, DELETE, PATCH, OPTIONS

---

## 5. Environment Variables Mapping

Create a `.env.local` file in your Next.js project root with the following variables:

```env
# Appwrite Configuration
NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
APPWRITE_API_KEY=your_api_key_here

# Database Configuration
NEXT_PUBLIC_APPWRITE_DATABASE_ID=salis-touch-db
NEXT_PUBLIC_APPWRITE_PRODUCTS_COLLECTION_ID=products
NEXT_PUBLIC_APPWRITE_CATEGORIES_COLLECTION_ID=categories
NEXT_PUBLIC_APPWRITE_TESTIMONIALS_COLLECTION_ID=testimonials
NEXT_PUBLIC_APPWRITE_INQUIRIES_COLLECTION_ID=inquiries
NEXT_PUBLIC_APPWRITE_STORE_INFO_COLLECTION_ID=store-info
NEXT_PUBLIC_APPWRITE_ANALYTICS_COLLECTION_ID=analytics

# Storage Configuration
NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=product-images

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your_hashed_password_here

# Site Configuration
NEXT_PUBLIC_SITE_NAME=Salis Touch
NEXT_PUBLIC_SITE_URL=https://salistouch.com
NEXT_PUBLIC_STORE_NAME=Salis Touch Store
NEXT_PUBLIC_STORE_PHONE=******-0123
NEXT_PUBLIC_STORE_EMAIL=<EMAIL>
NEXT_PUBLIC_STORE_ADDRESS=123 Main Street, City, State 12345
```

### How to Obtain Each Value

- **NEXT_PUBLIC_APPWRITE_ENDPOINT**: Found in Settings → General
- **NEXT_PUBLIC_APPWRITE_PROJECT_ID**: Found at the top of any page in your project
- **APPWRITE_API_KEY**: Created in Settings → API Keys
- **Database and Collection IDs**: Use the exact IDs you created above
- **NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID**: Found in Storage section

---

## 6. Data Seeding (Optional)

### 6.1 Sample Store Information

Create a document in the `store-info` collection:

```json
{
  "name": "Salis Touch",
  "description": "Premium handcrafted products and personalized services for discerning customers.",
  "phone": "******-0123",
  "email": "<EMAIL>",
  "address": "{\"street\":\"123 Main Street\",\"city\":\"Anytown\",\"state\":\"CA\",\"zipCode\":\"12345\",\"country\":\"United States\"}",
  "hours": "[{\"dayOfWeek\":\"monday\",\"openTime\":\"09:00\",\"closeTime\":\"18:00\",\"isClosed\":false},{\"dayOfWeek\":\"tuesday\",\"openTime\":\"09:00\",\"closeTime\":\"18:00\",\"isClosed\":false},{\"dayOfWeek\":\"wednesday\",\"openTime\":\"09:00\",\"closeTime\":\"18:00\",\"isClosed\":false},{\"dayOfWeek\":\"thursday\",\"openTime\":\"09:00\",\"closeTime\":\"18:00\",\"isClosed\":false},{\"dayOfWeek\":\"friday\",\"openTime\":\"09:00\",\"closeTime\":\"18:00\",\"isClosed\":false},{\"dayOfWeek\":\"saturday\",\"openTime\":\"10:00\",\"closeTime\":\"17:00\",\"isClosed\":false},{\"dayOfWeek\":\"sunday\",\"openTime\":\"\",\"closeTime\":\"\",\"isClosed\":true}]",
  "aboutUs": "We are passionate about bringing you exceptional products that enhance your lifestyle.",
  "mission": "To provide premium quality products with exceptional customer service.",
  "vision": "To be the leading destination for discerning customers seeking quality and craftsmanship."
}
```

### 6.2 Sample Categories

Create documents in the `categories` collection:

```json
[
  {
    "name": "Home & Living",
    "description": "Beautiful items for your home",
    "slug": "home-living",
    "sortOrder": 1,
    "isActive": true
  },
  {
    "name": "Fashion & Accessories",
    "description": "Stylish fashion items and accessories",
    "slug": "fashion-accessories",
    "sortOrder": 2,
    "isActive": true
  },
  {
    "name": "Art & Crafts",
    "description": "Handmade art and craft items",
    "slug": "art-crafts",
    "sortOrder": 3,
    "isActive": true
  }
]
```

### 6.3 Sample Products

Create documents in the `products` collection:

```json
[
  {
    "name": "Handcrafted Ceramic Vase",
    "description": "Beautiful handcrafted ceramic vase perfect for any home decor. Made with premium materials and attention to detail.",
    "shortDescription": "Elegant ceramic vase for modern homes",
    "price": 89.99,
    "compareAtPrice": 120.0,
    "sku": "CV-001",
    "category": "Home & Living",
    "categoryId": "home-living-category-id",
    "images": [],
    "features": ["Handcrafted", "Premium Ceramic", "Modern Design"],
    "inStock": true,
    "stockQuantity": 15,
    "featured": true,
    "tags": ["ceramic", "vase", "home-decor"],
    "slug": "handcrafted-ceramic-vase"
  }
]
```

---

## 7. Verification Steps

### 7.1 Test Database Connection

1. Start your Next.js development server:

   ```bash
   npm run dev
   ```

2. Open your browser and navigate to `http://localhost:3000`

3. Check the browser console for any connection errors

4. Try accessing the admin dashboard at `http://localhost:3000/admin`

### 7.2 Test API Endpoints

You can test the connection using the browser's developer tools:

```javascript
// Test in browser console
fetch('/api/test-connection')
  .then((response) => response.json())
  .then((data) => console.log('Connection test:', data))
  .catch((error) => console.error('Connection error:', error));
```

### 7.3 Common Troubleshooting

**Connection Issues:**

1. Verify all environment variables are correctly set
2. Check that your API key has the correct scopes
3. Ensure CORS is configured for your domain
4. Verify the Appwrite endpoint URL is correct

**Permission Issues:**

1. Check collection permissions are set correctly
2. Verify API key permissions
3. Ensure bucket permissions allow public read access

**Data Issues:**

1. Verify collection IDs match your environment variables
2. Check attribute names and types match the schema
3. Ensure required fields are provided when creating documents

**Build Issues:**

1. Make sure all environment variables are available at build time
2. Check that `NEXT_PUBLIC_` prefix is used for client-side variables
3. Verify TypeScript types match your database schema

---

## Next Steps

After completing this setup:

1. **Test the Application**: Verify all features work correctly
2. **Add Sample Data**: Populate your collections with real data
3. **Configure Production**: Set up production environment variables
4. **Monitor Usage**: Use Appwrite's analytics to monitor API usage
5. **Backup Strategy**: Set up regular backups of your database

Your Appwrite backend is now fully configured and ready to power the Salis Touch showcase website!
