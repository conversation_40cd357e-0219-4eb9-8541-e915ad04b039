import { Client, Databases, Storage, Account, Query } from 'appwrite';

// Environment variables validation
const requiredEnvVars = {
  endpoint: process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT,
  projectId: process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
  databaseId: process.env.NEXT_PUBLIC_APPWRITE_DATABASE_ID,
  storageBucketId: process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID,
} as const;

// Validate environment variables
Object.entries(requiredEnvVars).forEach(([key, value]) => {
  if (!value) {
    throw new Error(`Missing required environment variable: NEXT_PUBLIC_APPWRITE_${key.toUpperCase()}`);
  }
});

// Initialize Appwrite client
export const client = new Client()
  .setEndpoint(requiredEnvVars.endpoint!)
  .setProject(requiredEnvVars.projectId!);

// Initialize services
export const databases = new Databases(client);
export const storage = new Storage(client);
export const account = new Account(client);

// Database and collection IDs
export const DATABASE_ID = requiredEnvVars.databaseId!;
export const STORAGE_BUCKET_ID = requiredEnvVars.storageBucketId!;

export const COLLECTION_IDS = {
  PRODUCTS: process.env.NEXT_PUBLIC_APPWRITE_PRODUCTS_COLLECTION_ID!,
  CATEGORIES: process.env.NEXT_PUBLIC_APPWRITE_CATEGORIES_COLLECTION_ID!,
  TESTIMONIALS: process.env.NEXT_PUBLIC_APPWRITE_TESTIMONIALS_COLLECTION_ID!,
  INQUIRIES: process.env.NEXT_PUBLIC_APPWRITE_INQUIRIES_COLLECTION_ID!,
  STORE_INFO: process.env.NEXT_PUBLIC_APPWRITE_STORE_INFO_COLLECTION_ID!,
  ANALYTICS: process.env.NEXT_PUBLIC_APPWRITE_ANALYTICS_COLLECTION_ID!,
} as const;

// Query helper
export { Query };

// File upload helper
export const uploadFile = async (file: File): Promise<string> => {
  try {
    const response = await storage.createFile(
      STORAGE_BUCKET_ID,
      'unique()',
      file
    );
    return response.$id;
  } catch (error) {
    console.error('Error uploading file:', error);
    throw new Error('Failed to upload file');
  }
};

// Get file URL helper
export const getFileUrl = (fileId: string): string => {
  return storage.getFileView(STORAGE_BUCKET_ID, fileId).toString();
};

// Delete file helper
export const deleteFile = async (fileId: string): Promise<void> => {
  try {
    await storage.deleteFile(STORAGE_BUCKET_ID, fileId);
  } catch (error) {
    console.error('Error deleting file:', error);
    throw new Error('Failed to delete file');
  }
};
