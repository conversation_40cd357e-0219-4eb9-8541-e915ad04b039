'use client';

import Link from 'next/link';
import { Award, Users, Heart, MapPin, Clock, Star, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useStoreInfo } from '@/hooks/use-store-info';
import { formatDate } from '@/lib/utils';
import Image from 'next/image';

const values = [
  {
    icon: Award,
    title: 'Quality First',
    description: 'We never compromise on quality. Every product we offer meets our rigorous standards for craftsmanship and durability.',
  },
  {
    icon: Users,
    title: 'Customer Focus',
    description: 'Our customers are at the heart of everything we do. We listen, understand, and deliver solutions that exceed expectations.',
  },
  {
    icon: Heart,
    title: 'Passion & Care',
    description: 'We genuinely care about our customers and community. This passion drives us to provide exceptional service every day.',
  },
  {
    icon: MapPin,
    title: 'Local Commitment',
    description: 'As a local business, we\'re committed to supporting our community and building lasting relationships with our neighbors.',
  },
];

const milestones = [
  {
    year: '2020',
    title: 'Founded',
    description: 'Started with a vision to bring quality products and exceptional service to our community.',
  },
  {
    year: '2021',
    title: 'First Expansion',
    description: 'Expanded our product line and moved to our current location to better serve our growing customer base.',
  },
  {
    year: '2022',
    title: 'Community Recognition',
    description: 'Received local business excellence award for outstanding customer service and community involvement.',
  },
  {
    year: '2023',
    title: 'Digital Presence',
    description: 'Launched our online showcase to help customers discover our products and plan their visits.',
  },
];

export function AboutContent() {
  const { storeInfo } = useStoreInfo();

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">
          About {storeInfo?.name || 'Salis Touch'}
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          {storeInfo?.aboutUs || 
            'We are more than just a store – we\'re your neighbors, your trusted advisors, and your partners in finding exactly what you need. Our commitment to quality and service has made us a cornerstone of the community.'}
        </p>
      </div>

      {/* Mission & Vision */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
        {storeInfo?.mission && (
          <Card className="border-l-4 border-l-primary">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-primary" />
                <span>Our Mission</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                {storeInfo.mission}
              </p>
            </CardContent>
          </Card>
        )}

        {storeInfo?.vision && (
          <Card className="border-l-4 border-l-secondary">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Award className="h-5 w-5 text-secondary" />
                <span>Our Vision</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground leading-relaxed">
                {storeInfo.vision}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Values */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Values</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            These core values guide everything we do and shape the experience we provide to our customers.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {values.map((value, index) => (
            <Card key={index} className="text-center hover:shadow-lg transition-shadow">
              <CardContent className="p-6 space-y-4">
                <div className="w-12 h-12 mx-auto bg-primary/10 rounded-lg flex items-center justify-center">
                  <value.icon className="h-6 w-6 text-primary" />
                </div>
                <div className="space-y-2">
                  <h3 className="font-semibold text-lg">{value.title}</h3>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {value.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Our Story Timeline */}
      <div className="mb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Journey</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            From humble beginnings to becoming a trusted name in the community, here&apos;s our story.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {milestones.map((milestone, index) => (
            <Card key={index} className="relative">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <Badge variant="outline" className="text-primary border-primary">
                    {milestone.year}
                  </Badge>
                  <CardTitle className="text-lg">{milestone.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {milestone.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Team Section */}
      {storeInfo?.teamMembers && storeInfo.teamMembers.length > 0 && (
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Meet Our Team</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              The passionate people behind our success. Each team member brings unique expertise and dedication to serving you better.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {storeInfo.teamMembers.map((member) => (
              <Card key={member.id} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6 space-y-4">
                  <div className="w-24 h-24 mx-auto bg-muted rounded-full flex items-center justify-center overflow-hidden">
                    {member.image ? (
                      <Image
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <Users className="h-12 w-12 text-muted-foreground" />
                    )}
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold text-xl">{member.name}</h3>
                    <p className="text-primary font-medium">{member.position}</p>
                    {member.bio && (
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {member.bio}
                      </p>
                    )}
                  </div>
                  {(member.email || member.phone) && (
                    <div className="pt-2 border-t space-y-1">
                      {member.email && (
                        <p className="text-xs text-muted-foreground">{member.email}</p>
                      )}
                      {member.phone && (
                        <p className="text-xs text-muted-foreground">{member.phone}</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* Store Information */}
      <div className="bg-muted/30 rounded-lg p-8 mb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          <div>
            <h2 className="text-2xl font-bold mb-4">Visit Our Store</h2>
            <p className="text-muted-foreground mb-6 leading-relaxed">
              Experience our products in person and get personalized recommendations from our knowledgeable team. 
              We&apos;re here to help you find exactly what you&apos;re looking for.
            </p>
            
            <div className="space-y-3 mb-6">
              {storeInfo?.address && (
                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <p className="font-medium">{storeInfo.address.street}</p>
                    <p className="text-muted-foreground">
                      {storeInfo.address.city}, {storeInfo.address.state} {storeInfo.address.zipCode}
                    </p>
                  </div>
                </div>
              )}
              
              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-primary" />
                <p className="text-muted-foreground">
                  Open Monday - Saturday | Closed Sundays
                </p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" asChild>
                <Link href="/contact">
                  <MapPin className="mr-2 h-5 w-5" />
                  Visit Us Today
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/products">
                  Browse Products
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <Card>
              <CardContent className="p-6 text-center">
                <Award className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold text-lg mb-2">Quality Guarantee</h3>
                <p className="text-sm text-muted-foreground">
                  We stand behind every product we sell with our satisfaction guarantee.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6 text-center">
                <Heart className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="font-semibold text-lg mb-2">Personal Service</h3>
                <p className="text-sm text-muted-foreground">
                  Get expert advice and personalized recommendations from our friendly team.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Ready to Experience the Difference?</h2>
        <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
          Come visit us and discover why our customers keep coming back. We&apos;re excited to meet you and help you find exactly what you need.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link href="/contact">Plan Your Visit</Link>
          </Button>
          <Button size="lg" variant="outline" asChild>
            <Link href="/testimonials">Read Customer Reviews</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
