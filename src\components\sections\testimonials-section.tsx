'use client';

import Link from 'next/link';
import { <PERSON>R<PERSON>, <PERSON>, Quote, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useState, useEffect } from 'react';
import { testimonialService } from '@/lib/database';
import { formatDate } from '@/lib/utils';
import type { Testimonial } from '@/types';
import Image from 'next/image';

export function TestimonialsSection() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const result = await testimonialService.getFeaturedTestimonials(3);
        setTestimonials(result);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-muted-foreground'
        }`}
      />
    ));
  };

  if (loading) {
    return (
      <section className="py-16 lg:py-24 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Customers Say</h2>
            <p className="text-lg text-muted-foreground">
              Real experiences from our valued customers
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 3 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6 space-y-4">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-4 bg-muted rounded w-full" />
                  <div className="h-4 bg-muted rounded w-2/3" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 lg:py-24 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Customers Say</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Don&apos;t just take our word for it. Here&apos;s what our satisfied customers have to say about their experience with us.
          </p>
        </div>

        {testimonials.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {testimonials.map((testimonial) => (
                <Card key={testimonial.$id} className="hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-6 space-y-4">
                    {/* Quote Icon */}
                    <div className="flex justify-between items-start">
                      <Quote className="h-8 w-8 text-primary/20" />
                      <div className="flex items-center space-x-1">
                        {renderStars(testimonial.rating)}
                      </div>
                    </div>

                    {/* Testimonial Title */}
                    {testimonial.title && (
                      <h3 className="font-semibold text-lg">{testimonial.title}</h3>
                    )}

                    {/* Testimonial Content */}
                    <blockquote className="text-muted-foreground leading-relaxed">
                      &quot;{testimonial.content}&quot;
                    </blockquote>

                    {/* Customer Info */}
                    <div className="flex items-center space-x-3 pt-4 border-t">
                      <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                        {testimonial.customerImage ? (
                          <Image
                            src={testimonial.customerImage}
                            alt={testimonial.customerName}
                            className="w-full h-full rounded-full object-cover"
                          />
                        ) : (
                          <User className="h-5 w-5 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{testimonial.customerName}</p>
                        <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                          {testimonial.location && <span>{testimonial.location}</span>}
                          {testimonial.purchaseDate && (
                            <>
                              {testimonial.location && <span>•</span>}
                              <span>{formatDate(testimonial.purchaseDate)}</span>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Verified Badge */}
                    <div className="flex justify-end">
                      <Badge variant="outline" className="text-xs">
                        Verified Customer
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="text-center">
              <Button size="lg" variant="outline" asChild>
                <Link href="/testimonials">
                  Read More Reviews
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold mb-2">Customer Reviews Coming Soon</h3>
            <p className="text-muted-foreground mb-6">
              We&apos;re collecting feedback from our valued customers. Visit our store to share your experience!
            </p>
            <Button asChild>
              <Link href="/contact">Visit Our Store</Link>
            </Button>
          </div>
        )}

        {/* Call to Action for Reviews */}
        <div className="mt-16 text-center bg-primary/5 rounded-lg p-8">
          <h3 className="text-2xl font-bold mb-4">Share Your Experience</h3>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            We&apos;d love to hear about your experience with our products and services. 
            Your feedback helps us improve and helps other customers make informed decisions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild>
              <Link href="/testimonials#write-review">Write a Review</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
