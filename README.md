# Salis Touch - Showcase Website

A comprehensive showcase website built with Next.js 15 that demonstrates products/services without enabling online transactions. The website serves as a digital storefront that drives in-person visits and purchases.

## Features

### Customer-Facing Features

- **Product Catalog**: Complete product showcase with detailed descriptions, image galleries, pricing, and specifications
- **Store Information**: Location, hours, contact details prominently displayed
- **In-Person Focus**: "Visit Us" and "Call to Order" buttons instead of e-commerce functionality
- **Customer Testimonials**: Reviews and feedback section
- **About Us**: Business story, mission, vision, and team information
- **Contact Form**: Inquiry system for customer questions (not orders)
- **Responsive Design**: Mobile and desktop optimized

### Admin Features

- **Protected Dashboard**: Secure admin access with authentication
- **Product Management**: Add, edit, delete, and update inventory status
- **Inquiry Management**: Handle customer inquiries and communications
- **Analytics Dashboard**: Basic metrics for page views and popular products
- **Store Management**: Update hours, contact details, and announcements
- **Content Management**: Update policies, about page, and store information

## Technology Stack

- **Framework**: Next.js 15 with App Router
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS v4 with CSS variables
- **Database**: Appwrite (Backend as a Service)
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Fonts**: Geist Sans and Geist Mono

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- Appwrite account and project

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd salis-touch
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Set up environment variables**

   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your Appwrite configuration:

   ```env
   # Appwrite Configuration
   NEXT_PUBLIC_APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
   NEXT_PUBLIC_APPWRITE_PROJECT_ID=your_project_id_here
   APPWRITE_API_KEY=your_api_key_here

   # Database Configuration
   NEXT_PUBLIC_APPWRITE_DATABASE_ID=salis-touch-db
   NEXT_PUBLIC_APPWRITE_PRODUCTS_COLLECTION_ID=products
   NEXT_PUBLIC_APPWRITE_CATEGORIES_COLLECTION_ID=categories
   NEXT_PUBLIC_APPWRITE_TESTIMONIALS_COLLECTION_ID=testimonials
   NEXT_PUBLIC_APPWRITE_INQUIRIES_COLLECTION_ID=inquiries
   NEXT_PUBLIC_APPWRITE_STORE_INFO_COLLECTION_ID=store-info
   NEXT_PUBLIC_APPWRITE_ANALYTICS_COLLECTION_ID=analytics

   # Storage Configuration
   NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID=product-images

   # Site Configuration
   NEXT_PUBLIC_SITE_NAME=Salis Touch
   NEXT_PUBLIC_SITE_URL=https://salistouch.com
   NEXT_PUBLIC_STORE_NAME=Salis Touch Store
   NEXT_PUBLIC_STORE_PHONE=******-0123
   NEXT_PUBLIC_STORE_EMAIL=<EMAIL>
   NEXT_PUBLIC_STORE_ADDRESS=123 Main Street, City, State 12345
   ```

4. **Set up Appwrite**

   - Create a new Appwrite project
   - Create a database with the collections specified in the environment variables
   - Set up the required attributes for each collection (see Database Schema section)
   - Create a storage bucket for product images
   - Configure permissions for public read access

5. **Run the development server**

   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the website.

### Admin Access

- Navigate to `/admin` to access the admin dashboard
- Default credentials:
  - Email: `<EMAIL>`
  - Password: `admin123`

## Database Schema

### Products Collection

```typescript
{
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  compareAtPrice?: number;
  sku?: string;
  category: string;
  categoryId: string;
  images: string[]; // Appwrite file IDs
  specifications?: { name: string; value: string }[];
  features?: string[];
  inStock: boolean;
  stockQuantity?: number;
  featured: boolean;
  tags?: string[];
  slug: string;
  // ... additional fields
}
```

### Categories Collection

```typescript
{
  name: string;
  description?: string;
  slug: string;
  image?: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
}
```

### Store Info Collection

```typescript
{
  name: string;
  description: string;
  phone: string;
  email: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  }
  hours: {
    dayOfWeek: string;
    openTime: string;
    closeTime: string;
    isClosed: boolean;
  }
  [];
  // ... additional fields
}
```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin dashboard routes
│   ├── products/          # Product catalog and detail pages
│   ├── contact/           # Contact page
│   ├── about/             # About page
│   ├── testimonials/      # Testimonials page
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── ui/                # shadcn/ui components
│   ├── admin/             # Admin-specific components
│   ├── sections/          # Page sections
│   └── ...                # Other components
├── lib/                   # Utility functions and configurations
│   ├── appwrite.ts        # Appwrite client setup
│   ├── database.ts        # Database service layer
│   ├── utils.ts           # Utility functions
│   └── validations.ts     # Zod schemas
├── hooks/                 # Custom React hooks
├── types/                 # TypeScript type definitions
└── ...
```

## Key Features Implementation

### No E-commerce Functionality

- No shopping cart or checkout process
- No payment integration
- "Visit Store" and "Call to Order" CTAs instead of "Add to Cart"
- Focus on driving in-person visits

### SEO Optimization

- Proper meta tags and structured data
- Local business schema markup
- Optimized for local search discovery
- Fast loading with Next.js optimization

### Responsive Design

- Mobile-first approach
- Optimized for all screen sizes
- Touch-friendly interface
- Fast loading on mobile networks

## Deployment

### Vercel (Recommended)

1. Connect your repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Other Platforms

The application can be deployed to any platform that supports Node.js applications:

- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue in the repository
- Contact the development team
- Check the documentation

## Roadmap

- [ ] Enhanced analytics dashboard
- [ ] Email notification system
- [ ] Advanced product filtering
- [ ] Multi-language support
- [ ] Enhanced SEO features
- [ ] Performance optimizations
