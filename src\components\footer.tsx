'use client';

import Link from 'next/link';
import { Phone, Mail, MapPin, Clock, Facebook, Instagram, Twitter } from 'lucide-react';
import { useStoreInfo } from '@/hooks/use-store-info';
import { formatPhoneNumber, isStoreOpen, getNextOpeningTime } from '@/lib/utils';

const quickLinks = [
  { name: 'Home', href: '/' },
  { name: 'Products', href: '/products' },
  { name: 'About Us', href: '/about' },
  { name: 'Testimonials', href: '/testimonials' },
  { name: 'Contact', href: '/contact' },
];

const customerService = [
  { name: 'Visit Our Store', href: '/contact' },
  { name: 'Call Us', href: 'tel:******-0123' },
  { name: 'Store Hours', href: '/contact#hours' },
  { name: 'Directions', href: '/contact#location' },
];

export function Footer() {
  const { storeInfo } = useStoreInfo();
  const currentYear = new Date().getFullYear();
  const isStoreCurrentlyOpen = storeInfo?.hours ? isStoreOpen(storeInfo.hours) : false;
  const nextOpeningTime = storeInfo?.hours ? getNextOpeningTime(storeInfo.hours) : null;

  return (
    <footer className="bg-muted/50 border-t">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Store Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">
              {storeInfo?.name || 'Salis Touch'}
            </h3>
            <p className="text-sm text-muted-foreground">
              {storeInfo?.description || 
                'Discover our premium collection of products and services. Visit our store for personalized assistance and expert recommendations.'}
            </p>
            
            {/* Store Status */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm">
                <Clock className="h-4 w-4" />
                <span className={isStoreCurrentlyOpen ? 'text-green-600' : 'text-muted-foreground'}>
                  {isStoreCurrentlyOpen ? 'Open Now' : 'Currently Closed'}
                </span>
              </div>
              {!isStoreCurrentlyOpen && nextOpeningTime && (
                <p className="text-xs text-muted-foreground ml-6">
                  Next opening: {nextOpeningTime}
                </p>
              )}
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Quick Links</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Customer Service */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Customer Service</h3>
            <ul className="space-y-2">
              {customerService.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-primary transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Contact Info</h3>
            <div className="space-y-3">
              {storeInfo?.phone && (
                <div className="flex items-center space-x-2 text-sm">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={`tel:${storeInfo.phone}`}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {formatPhoneNumber(storeInfo.phone)}
                  </a>
                </div>
              )}
              
              {storeInfo?.email && (
                <div className="flex items-center space-x-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <a
                    href={`mailto:${storeInfo.email}`}
                    className="text-muted-foreground hover:text-primary transition-colors"
                  >
                    {storeInfo.email}
                  </a>
                </div>
              )}
              
              {storeInfo?.address && (
                <div className="flex items-start space-x-2 text-sm">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <div className="text-muted-foreground">
                    <div>{storeInfo.address.street}</div>
                    <div>
                      {storeInfo.address.city}, {storeInfo.address.state}{' '}
                      {storeInfo.address.zipCode}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Social Media Links */}
            {storeInfo?.socialMedia && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Follow Us</h4>
                <div className="flex space-x-3">
                  {storeInfo.socialMedia.facebook && (
                    <a
                      href={storeInfo.socialMedia.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      <Facebook className="h-5 w-5" />
                      <span className="sr-only">Facebook</span>
                    </a>
                  )}
                  {storeInfo.socialMedia.instagram && (
                    <a
                      href={storeInfo.socialMedia.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      <Instagram className="h-5 w-5" />
                      <span className="sr-only">Instagram</span>
                    </a>
                  )}
                  {storeInfo.socialMedia.twitter && (
                    <a
                      href={storeInfo.socialMedia.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      <Twitter className="h-5 w-5" />
                      <span className="sr-only">Twitter</span>
                    </a>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-8 pt-8 border-t">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
            <p className="text-sm text-muted-foreground">
              © {currentYear} {storeInfo?.name || 'Salis Touch'}. All rights reserved.
            </p>
            <div className="flex space-x-4 text-sm text-muted-foreground">
              <Link href="/privacy" className="hover:text-primary transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="hover:text-primary transition-colors">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
