import { <PERSON>ada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ContactForm } from '@/components/contact-form';
import { StoreLocation } from '@/components/store-location';
import { StoreHours } from '@/components/store-hours';

export const metadata: Metadata = {
  title: 'Contact Us - Salis Touch',
  description: 'Visit our store, call us, or send us a message. We\'re here to help you find exactly what you\'re looking for.',
  keywords: 'contact, visit, store location, hours, phone, email, directions',
};

export default function ContactPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Contact Us</h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              We&apos;d love to hear from you! Visit our store for a personalized shopping experience, 
              call us for quick questions, or send us a message using the form below.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-2xl font-semibold mb-6">Send Us a Message</h2>
              <ContactForm />
            </div>

            {/* Store Information */}
            <div className="space-y-8">
              <StoreLocation />
              <StoreHours />
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
