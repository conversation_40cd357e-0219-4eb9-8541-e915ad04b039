{"projectId": "6874acbf001bf118e099", "endpoint": "https://fra.cloud.appwrite.io/v1", "projectName": "salis-touch", "settings": {"services": {"account": true, "avatars": true, "databases": true, "locale": true, "health": true, "storage": true, "teams": true, "users": true, "sites": true, "functions": true, "graphql": true, "messaging": true}, "auth": {"methods": {"jwt": true, "phone": true, "invites": true, "anonymous": true, "email-otp": true, "magic-url": true, "email-password": true}, "security": {"duration": ********, "limit": 0, "sessionsLimit": 10, "passwordHistory": 0, "passwordDictionary": false, "personalDataCheck": false, "sessionAlerts": false, "mockNumbers": []}}}, "databases": [{"$id": "salis-touch-db", "name": "Salis Touch Database", "enabled": true}], "collections": [{"$id": "products", "$permissions": ["read(\"any\")", "create(\"users\")", "update(\"users\")", "delete(\"users\")"], "databaseId": "salis-touch-db", "name": "Products", "enabled": true, "documentSecurity": false, "attributes": [{"key": "name", "type": "string", "required": true, "array": false, "size": 200, "encrypt": false}, {"key": "description", "type": "string", "required": true, "array": false, "size": 5000, "encrypt": false}, {"key": "shortDescription", "type": "string", "required": false, "array": false, "size": 300, "default": null, "encrypt": false}, {"key": "price", "type": "double", "required": true, "array": false, "min": 0, "max": 999999}, {"key": "compareAtPrice", "type": "double", "required": false, "array": false, "min": 0, "max": 999999, "default": null}, {"key": "sku", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "category", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "categoryId", "type": "string", "required": true, "array": false, "size": 50, "encrypt": false}, {"key": "images", "type": "string", "required": false, "array": true, "size": 50, "default": null, "encrypt": false}, {"key": "specifications", "type": "string", "required": false, "array": false, "size": 2000, "default": null, "encrypt": false}, {"key": "features", "type": "string", "required": false, "array": true, "size": 100, "default": null, "encrypt": false}, {"key": "inStock", "type": "boolean", "required": true, "array": false}, {"key": "stockQuantity", "type": "integer", "required": false, "array": false, "min": 0, "max": 999999, "default": null}, {"key": "featured", "type": "boolean", "required": true, "array": false}, {"key": "tags", "type": "string", "required": false, "array": true, "size": 50, "default": null, "encrypt": false}, {"key": "weight", "type": "double", "required": false, "array": false, "min": 0, "max": null, "default": null}, {"key": "dimensions", "type": "string", "required": false, "array": false, "size": 500, "default": null, "encrypt": false}, {"key": "materials", "type": "string", "required": false, "array": true, "size": 100, "default": null, "encrypt": false}, {"key": "careInstructions", "type": "string", "required": false, "array": false, "size": 1000, "default": null, "encrypt": false}, {"key": "seo<PERSON><PERSON>le", "type": "string", "required": false, "array": false, "size": 60, "default": null, "encrypt": false}, {"key": "seoDescription", "type": "string", "required": false, "array": false, "size": 160, "default": null, "encrypt": false}, {"key": "slug", "type": "string", "required": true, "array": false, "size": 200, "encrypt": false}], "indexes": [{"key": "slug_index", "type": "unique", "status": "available", "attributes": ["slug"], "orders": ["ASC"]}, {"key": "category_index", "type": "key", "status": "available", "attributes": ["categoryId"], "orders": ["ASC"]}, {"key": "featured_index", "type": "key", "status": "available", "attributes": ["featured"], "orders": ["ASC"]}, {"key": "inStock_index", "type": "key", "status": "available", "attributes": ["inStock"], "orders": ["ASC"]}]}, {"$id": "categories", "$permissions": ["read(\"any\")", "create(\"users\")", "update(\"users\")", "delete(\"users\")"], "databaseId": "salis-touch-db", "name": "Categories", "enabled": true, "documentSecurity": false, "attributes": [{"key": "name", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "description", "type": "string", "required": false, "array": false, "size": 500, "default": null, "encrypt": false}, {"key": "slug", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "image", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "parentId", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "sortOrder", "type": "integer", "required": false, "array": false, "min": 0, "max": 999999999999, "default": 0}, {"key": "isActive", "type": "boolean", "required": false, "array": false, "default": true}, {"key": "seo<PERSON><PERSON>le", "type": "string", "required": false, "array": false, "size": 60, "default": null, "encrypt": false}, {"key": "seoDescription", "type": "string", "required": false, "array": false, "size": 160, "default": null, "encrypt": false}], "indexes": [{"key": "slug_index", "type": "unique", "status": "available", "attributes": ["slug"], "orders": ["ASC"]}, {"key": "active_sort_index", "type": "key", "status": "available", "attributes": ["isActive", "sortOrder"], "orders": ["ASC", "ASC"]}]}, {"$id": "store-info", "$permissions": ["read(\"any\")", "create(\"users\")", "update(\"users\")", "delete(\"users\")"], "databaseId": "salis-touch-db", "name": "Store Information", "enabled": true, "documentSecurity": false, "attributes": [{"key": "name", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "description", "type": "string", "required": true, "array": false, "size": 1000, "encrypt": false}, {"key": "phone", "type": "string", "required": true, "array": false, "size": 20, "encrypt": false}, {"key": "email", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "address", "type": "string", "required": true, "array": false, "size": 1000, "encrypt": false}, {"key": "hours", "type": "string", "required": true, "array": false, "size": 2000, "encrypt": false}, {"key": "socialMedia", "type": "string", "required": false, "array": false, "size": 1000, "default": null, "encrypt": false}, {"key": "announcements", "type": "string", "required": false, "array": false, "size": 2000, "default": null, "encrypt": false}, {"key": "policies", "type": "string", "required": false, "array": false, "size": 5000, "default": null, "encrypt": false}, {"key": "aboutUs", "type": "string", "required": false, "array": false, "size": 2000, "default": null, "encrypt": false}, {"key": "mission", "type": "string", "required": false, "array": false, "size": 500, "default": null, "encrypt": false}, {"key": "vision", "type": "string", "required": false, "array": false, "size": 500, "default": null, "encrypt": false}, {"key": "teamMembers", "type": "string", "required": false, "array": false, "size": 30, "default": null, "encrypt": false}], "indexes": []}, {"$id": "testimonials", "$permissions": ["read(\"any\")", "create(\"any\")", "update(\"users\")", "delete(\"users\")"], "databaseId": "salis-touch-db", "name": "Testimonials", "enabled": true, "documentSecurity": false, "attributes": [{"key": "customerName", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "customerEmail", "type": "string", "required": false, "array": false, "size": 100, "default": null, "encrypt": false}, {"key": "customerImage", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "rating", "type": "integer", "required": true, "array": false, "min": 1, "max": 5}, {"key": "title", "type": "string", "required": false, "array": false, "size": 200, "default": null, "encrypt": false}, {"key": "content", "type": "string", "required": true, "array": false, "size": 1000, "encrypt": false}, {"key": "productId", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "isApproved", "type": "boolean", "required": false, "array": false, "default": false}, {"key": "isFeatured", "type": "boolean", "required": false, "array": false, "default": false}, {"key": "location", "type": "string", "required": false, "array": false, "size": 100, "default": null, "encrypt": false}, {"key": "purchaseDate", "type": "string", "required": false, "array": false, "size": 20, "default": null, "encrypt": false}], "indexes": [{"key": "approved_index", "type": "key", "status": "available", "attributes": ["isApproved"], "orders": ["ASC"]}, {"key": "featured_index", "type": "key", "status": "available", "attributes": ["isFeatured"], "orders": ["ASC"]}]}, {"$id": "inquiries", "$permissions": ["read(\"users\")", "create(\"any\")", "update(\"users\")", "delete(\"users\")"], "databaseId": "salis-touch-db", "name": "Customer Inquiries", "enabled": true, "documentSecurity": false, "attributes": [{"key": "name", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "email", "type": "string", "required": true, "array": false, "size": 100, "encrypt": false}, {"key": "phone", "type": "string", "required": false, "array": false, "size": 20, "default": null, "encrypt": false}, {"key": "subject", "type": "string", "required": true, "array": false, "size": 200, "encrypt": false}, {"key": "message", "type": "string", "required": true, "array": false, "size": 1000, "encrypt": false}, {"key": "type", "type": "string", "required": true, "array": false, "size": 20, "encrypt": false}, {"key": "productId", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "status", "type": "string", "required": false, "array": false, "size": 20, "default": "new", "encrypt": false}, {"key": "priority", "type": "string", "required": false, "array": false, "size": 10, "default": "medium", "encrypt": false}, {"key": "assignedTo", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "notes", "type": "string", "required": false, "array": false, "size": 2000, "default": null, "encrypt": false}, {"key": "source", "type": "string", "required": false, "array": false, "size": 20, "default": "website", "encrypt": false}], "indexes": [{"key": "status_index", "type": "key", "status": "available", "attributes": ["status"], "orders": ["ASC"]}, {"key": "type_index", "type": "key", "status": "available", "attributes": ["type"], "orders": ["ASC"]}]}, {"$id": "analytics", "$permissions": ["read(\"users\")", "create(\"any\")", "update(\"users\")", "delete(\"users\")"], "databaseId": "salis-touch-db", "name": "Analytics Events", "enabled": true, "documentSecurity": false, "attributes": [{"key": "type", "type": "string", "required": true, "array": false, "size": 50, "encrypt": false}, {"key": "page", "type": "string", "required": false, "array": false, "size": 200, "default": null, "encrypt": false}, {"key": "productId", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "userAgent", "type": "string", "required": false, "array": false, "size": 500, "default": null, "encrypt": false}, {"key": "ip<PERSON><PERSON><PERSON>", "type": "string", "required": false, "array": false, "size": 50, "default": null, "encrypt": false}, {"key": "referrer", "type": "string", "required": false, "array": false, "size": 200, "default": null, "encrypt": false}, {"key": "sessionId", "type": "string", "required": false, "array": false, "size": 100, "default": null, "encrypt": false}, {"key": "metadata", "type": "string", "required": false, "array": false, "size": 1000, "default": null, "encrypt": false}], "indexes": [{"key": "type_date_index", "type": "key", "status": "available", "attributes": ["type", "$createdAt"], "orders": ["ASC", "ASC"]}]}], "buckets": [{"$id": "product-images", "$permissions": ["read(\"any\")", "create(\"users\")", "update(\"users\")", "delete(\"users\")"], "fileSecurity": true, "name": "Product Images", "enabled": true, "maximumFileSize": 5242880, "allowedFileExtensions": ["jpg", "jpeg", "png", "webp", "gif"], "compression": "gzip", "encryption": true, "antivirus": true}]}