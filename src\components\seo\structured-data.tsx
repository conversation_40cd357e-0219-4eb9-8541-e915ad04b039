'use client';

import type { Product, StoreInfo } from '@/types';

interface LocalBusinessStructuredDataProps {
  storeInfo?: StoreInfo;
}

export function LocalBusinessStructuredData({ storeInfo }: LocalBusinessStructuredDataProps) {
  if (!storeInfo) return null;

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    name: storeInfo.name,
    description: storeInfo.description,
    url: process.env.NEXT_PUBLIC_SITE_URL,
    telephone: storeInfo.phone,
    email: storeInfo.email,
    address: {
      '@type': 'PostalAddress',
      streetAddress: storeInfo.address.street,
      addressLocality: storeInfo.address.city,
      addressRegion: storeInfo.address.state,
      postalCode: storeInfo.address.zipCode,
      addressCountry: storeInfo.address.country,
    },
    geo: storeInfo.address.coordinates ? {
      '@type': 'GeoCoordinates',
      latitude: storeInfo.address.coordinates.latitude,
      longitude: storeInfo.address.coordinates.longitude,
    } : undefined,
    openingHoursSpecification: storeInfo.hours?.map(hour => ({
      '@type': 'OpeningHoursSpecification',
      dayOfWeek: `https://schema.org/${hour.dayOfWeek.charAt(0).toUpperCase() + hour.dayOfWeek.slice(1)}`,
      opens: hour.isClosed ? undefined : hour.openTime,
      closes: hour.isClosed ? undefined : hour.closeTime,
    })).filter(hour => hour.opens && hour.closes),
    sameAs: storeInfo.socialMedia ? Object.values(storeInfo.socialMedia).filter(Boolean) : [],
    priceRange: '$',
    currenciesAccepted: 'USD',
    paymentAccepted: 'Cash, Credit Card',
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

interface ProductStructuredDataProps {
  product: Product;
  storeInfo: StoreInfo | null | undefined;
}

export function ProductStructuredData({ product, storeInfo }: ProductStructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    sku: product.sku,
    brand: {
      '@type': 'Brand',
      name: storeInfo?.name || 'Salis Touch',
    },
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'USD',
      availability: product.inStock 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: storeInfo?.name || 'Salis Touch',
        url: process.env.NEXT_PUBLIC_SITE_URL,
      },
      priceValidUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 year from now
    },
    image: product.images?.map(imageId => 
      `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID}/files/${imageId}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`
    ),
    category: product.category,
    additionalProperty: product.specifications?.map(spec => ({
      '@type': 'PropertyValue',
      name: spec.name,
      value: spec.value,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

interface BreadcrumbStructuredDataProps {
  items: Array<{
    name: string;
    url: string;
  }>;
}

export function BreadcrumbStructuredData({ items }: BreadcrumbStructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

interface WebsiteStructuredDataProps {
  storeInfo?: StoreInfo;
}

export function WebsiteStructuredData({ storeInfo }: WebsiteStructuredDataProps) {
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: storeInfo?.name || 'Salis Touch',
    url: process.env.NEXT_PUBLIC_SITE_URL,
    description: storeInfo?.description || 'Premium products and services showcase',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${process.env.NEXT_PUBLIC_SITE_URL}/products?search={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

interface OrganizationStructuredDataProps {
  storeInfo?: StoreInfo;
}

export function OrganizationStructuredData({ storeInfo }: OrganizationStructuredDataProps) {
  if (!storeInfo) return null;

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: storeInfo.name,
    url: process.env.NEXT_PUBLIC_SITE_URL,
    description: storeInfo.description,
    telephone: storeInfo.phone,
    email: storeInfo.email,
    address: {
      '@type': 'PostalAddress',
      streetAddress: storeInfo.address.street,
      addressLocality: storeInfo.address.city,
      addressRegion: storeInfo.address.state,
      postalCode: storeInfo.address.zipCode,
      addressCountry: storeInfo.address.country,
    },
    sameAs: storeInfo.socialMedia ? Object.values(storeInfo.socialMedia).filter(Boolean) : [],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: storeInfo.phone,
      contactType: 'customer service',
      email: storeInfo.email,
    },
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}
