'use client';

import Link from 'next/link';
import { ArrowR<PERSON>, Users, Award, Heart, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useStoreInfo } from '@/hooks/use-store-info';
import Image from 'next/image';

const features = [
  {
    icon: Award,
    title: 'Premium Quality',
    description: 'We carefully select and curate only the finest products that meet our high standards of quality and craftsmanship.',
  },
  {
    icon: Users,
    title: 'Expert Service',
    description: 'Our knowledgeable team provides personalized recommendations and expert advice to help you find exactly what you need.',
  },
  {
    icon: Heart,
    title: 'Customer First',
    description: 'Your satisfaction is our priority. We build lasting relationships through exceptional service and genuine care.',
  },
  {
    icon: MapPin,
    title: 'Local Business',
    description: 'Proudly serving our community with a commitment to supporting local artisans and sustainable practices.',
  },
];

export function AboutSection() {
  const { storeInfo } = useStoreInfo();

  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-6">
            <div className="space-y-4">
              <h2 className="text-3xl md:text-4xl font-bold">
                About {storeInfo?.name || 'Our Store'}
              </h2>
              <p className="text-lg text-muted-foreground leading-relaxed">
                {storeInfo?.aboutUs || 
                  'We are passionate about bringing you exceptional products and services that enhance your lifestyle. Our commitment to quality and customer satisfaction has made us a trusted name in the community.'}
              </p>
            </div>

            {/* Mission & Vision */}
            <div className="space-y-4">
              {storeInfo?.mission && (
                <div>
                  <h3 className="text-xl font-semibold mb-2">Our Mission</h3>
                  <p className="text-muted-foreground">{storeInfo.mission}</p>
                </div>
              )}
              
              {storeInfo?.vision && (
                <div>
                  <h3 className="text-xl font-semibold mb-2">Our Vision</h3>
                  <p className="text-muted-foreground">{storeInfo.vision}</p>
                </div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" asChild>
                <Link href="/about">
                  Learn More About Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button size="lg" variant="outline" asChild>
                <Link href="/contact">Visit Our Store</Link>
              </Button>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow">
                <CardContent className="p-6 text-center space-y-4">
                  <div className="w-12 h-12 mx-auto bg-primary/10 rounded-lg flex items-center justify-center">
                    <feature.icon className="h-6 w-6 text-primary" />
                  </div>
                  <div className="space-y-2">
                    <h3 className="font-semibold">{feature.title}</h3>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Team Section */}
        {storeInfo?.teamMembers && storeInfo.teamMembers.length > 0 && (
          <div className="mt-16 pt-16 border-t">
            <div className="text-center mb-12">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">Meet Our Team</h3>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Our dedicated team of professionals is here to provide you with exceptional service and expertise.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {storeInfo.teamMembers.slice(0, 3).map((member) => (
                <Card key={member.id} className="text-center">
                  <CardContent className="p-6 space-y-4">
                    <div className="w-20 h-20 mx-auto bg-muted rounded-full flex items-center justify-center">
                      {member.image ? (
                        <Image
                          src={member.image}
                          alt={member.name}
                          className="w-full h-full rounded-full object-cover"
                        />
                      ) : (
                        <Users className="h-8 w-8 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-lg">{member.name}</h4>
                      <p className="text-primary font-medium">{member.position}</p>
                      {member.bio && (
                        <p className="text-sm text-muted-foreground mt-2">{member.bio}</p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {storeInfo.teamMembers.length > 3 && (
              <div className="text-center mt-8">
                <Button variant="outline" asChild>
                  <Link href="/about#team">
                    Meet All Team Members
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
}
