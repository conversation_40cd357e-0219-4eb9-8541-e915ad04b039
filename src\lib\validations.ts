import { z } from 'zod';

// Contact form validation
export const contactFormSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().optional().refine((val) => {
    if (!val) return true;
    const phoneRegex = /^\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;
    return phoneRegex.test(val);
  }, 'Please enter a valid phone number'),
  subject: z.string().min(5, 'Subject must be at least 5 characters').max(200, 'Subject must be less than 200 characters'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(1000, 'Message must be less than 1000 characters'),
  type: z.enum(['general', 'product', 'appointment', 'complaint', 'other']),
  productId: z.string().optional(),
});

export type ContactFormData = z.infer<typeof contactFormSchema>;

// Product form validation
export const productFormSchema = z.object({
  name: z.string().min(2, 'Product name must be at least 2 characters').max(200, 'Product name must be less than 200 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  shortDescription: z.string().max(300, 'Short description must be less than 300 characters').optional(),
  price: z.number().min(0, 'Price must be a positive number'),
  compareAtPrice: z.number().min(0, 'Compare at price must be a positive number').optional(),
  sku: z.string().max(50, 'SKU must be less than 50 characters').optional(),
  categoryId: z.string().min(1, 'Please select a category'),
  specifications: z.array(z.object({
    name: z.string().min(1, 'Specification name is required'),
    value: z.string().min(1, 'Specification value is required'),
  })).optional(),
  features: z.array(z.string()).optional(),
  inStock: z.boolean(),
  stockQuantity: z.number().min(0, 'Stock quantity must be a positive number').optional(),
  featured: z.boolean(),
  tags: z.array(z.string()).optional(),
  weight: z.number().min(0, 'Weight must be a positive number').optional(),
  dimensions: z.object({
    length: z.number().min(0).optional(),
    width: z.number().min(0).optional(),
    height: z.number().min(0).optional(),
    unit: z.enum(['cm', 'in']),
  }).optional(),
  materials: z.array(z.string()).optional(),
  careInstructions: z.string().optional(),
  seoTitle: z.string().max(60, 'SEO title must be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description must be less than 160 characters').optional(),
});

export type ProductFormData = z.infer<typeof productFormSchema>;

// Category form validation
export const categoryFormSchema = z.object({
  name: z.string().min(2, 'Category name must be at least 2 characters').max(100, 'Category name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  parentId: z.string().optional(),
  sortOrder: z.number().min(0, 'Sort order must be a positive number'),
  isActive: z.boolean(),
  seoTitle: z.string().max(60, 'SEO title must be less than 60 characters').optional(),
  seoDescription: z.string().max(160, 'SEO description must be less than 160 characters').optional(),
});

export type CategoryFormData = z.infer<typeof categoryFormSchema>;

// Testimonial form validation
export const testimonialFormSchema = z.object({
  customerName: z.string().min(2, 'Customer name must be at least 2 characters').max(100, 'Customer name must be less than 100 characters'),
  customerEmail: z.string().email('Please enter a valid email address').optional(),
  rating: z.number().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  title: z.string().max(200, 'Title must be less than 200 characters').optional(),
  content: z.string().min(10, 'Testimonial content must be at least 10 characters').max(1000, 'Testimonial content must be less than 1000 characters'),
  productId: z.string().optional(),
  location: z.string().max(100, 'Location must be less than 100 characters').optional(),
  purchaseDate: z.string().optional(),
});

export type TestimonialFormData = z.infer<typeof testimonialFormSchema>;

// Store info form validation
export const storeInfoFormSchema = z.object({
  name: z.string().min(2, 'Store name must be at least 2 characters').max(100, 'Store name must be less than 100 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  phone: z.string().refine((val) => {
    const phoneRegex = /^\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/;
    return phoneRegex.test(val);
  }, 'Please enter a valid phone number'),
  email: z.string().email('Please enter a valid email address'),
  address: z.object({
    street: z.string().min(5, 'Street address must be at least 5 characters'),
    city: z.string().min(2, 'City must be at least 2 characters'),
    state: z.string().min(2, 'State must be at least 2 characters'),
    zipCode: z.string().min(5, 'ZIP code must be at least 5 characters'),
    country: z.string().min(2, 'Country must be at least 2 characters'),
    coordinates: z.object({
      latitude: z.number().min(-90).max(90),
      longitude: z.number().min(-180).max(180),
    }).optional(),
  }),
  hours: z.array(z.object({
    dayOfWeek: z.enum(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']),
    openTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter a valid time in HH:MM format'),
    closeTime: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Please enter a valid time in HH:MM format'),
    isClosed: z.boolean(),
  })),
  socialMedia: z.object({
    facebook: z.string().url('Please enter a valid URL').optional(),
    instagram: z.string().url('Please enter a valid URL').optional(),
    twitter: z.string().url('Please enter a valid URL').optional(),
    linkedin: z.string().url('Please enter a valid URL').optional(),
    youtube: z.string().url('Please enter a valid URL').optional(),
    tiktok: z.string().url('Please enter a valid URL').optional(),
  }).optional(),
  aboutUs: z.string().optional(),
  mission: z.string().optional(),
  vision: z.string().optional(),
});

export type StoreInfoFormData = z.infer<typeof storeInfoFormSchema>;

// Search filters validation
export const productFiltersSchema = z.object({
  category: z.string().optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  inStock: z.boolean().optional(),
  featured: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().optional(),
});

export type ProductFiltersData = z.infer<typeof productFiltersSchema>;

// File upload validation
export const fileUploadSchema = z.object({
  file: z.instanceof(File).refine((file) => {
    return file.size <= 5 * 1024 * 1024; // 5MB
  }, 'File size must be less than 5MB').refine((file) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    return allowedTypes.includes(file.type);
  }, 'File must be a valid image (JPEG, PNG, WebP, or GIF)'),
});

export type FileUploadData = z.infer<typeof fileUploadSchema>;

// Admin login validation
export const adminLoginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

export type AdminLoginData = z.infer<typeof adminLoginSchema>;

// Inquiry update validation
export const inquiryUpdateSchema = z.object({
  status: z.enum(['new', 'in-progress', 'resolved', 'closed']),
  priority: z.enum(['low', 'medium', 'high']),
  assignedTo: z.string().optional(),
  notes: z.array(z.object({
    content: z.string().min(1, 'Note content is required'),
    createdBy: z.string().min(1, 'Created by is required'),
  })).optional(),
});

export type InquiryUpdateData = z.infer<typeof inquiryUpdateSchema>;
