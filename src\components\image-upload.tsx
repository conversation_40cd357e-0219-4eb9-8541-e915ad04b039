'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import Image from 'next/image';
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { uploadFile, deleteFile, getFileUrl } from '@/lib/appwrite';
import { formatFileSize, isImageFile } from '@/lib/utils';

interface ImageUploadProps {
  images: string[];
  onImagesChange: (images: string[]) => void;
  maxImages?: number;
  maxFileSize?: number; // in bytes
  className?: string;
}

export function ImageUpload({
  images,
  onImagesChange,
  maxImages = 10,
  maxFileSize = 5 * 1024 * 1024, // 5MB
  className,
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (images.length + acceptedFiles.length > maxImages) {
      toast.error(`Maximum ${maxImages} images allowed`);
      return;
    }

    setUploading(true);
    const newImages: string[] = [];

    try {
      for (const file of acceptedFiles) {
        // Validate file
        if (!isImageFile(file)) {
          toast.error(`${file.name} is not a valid image file`);
          continue;
        }

        if (file.size > maxFileSize) {
          toast.error(`${file.name} is too large. Maximum size is ${formatFileSize(maxFileSize)}`);
          continue;
        }

        try {
          // Set initial progress
          setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));

          // Upload file
          const fileId = await uploadFile(file);
          newImages.push(fileId);

          // Update progress
          setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));
          
          toast.success(`${file.name} uploaded successfully`);
        } catch (error) {
          console.error(`Error uploading ${file.name}:`, error);
          toast.error(`Failed to upload ${file.name}`);
        }
      }

      // Update images list
      if (newImages.length > 0) {
        onImagesChange([...images, ...newImages]);
      }
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  }, [images, maxImages, maxFileSize, onImagesChange]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp', '.gif']
    },
    multiple: true,
    disabled: uploading || images.length >= maxImages,
  });

  const removeImage = async (imageId: string, index: number) => {
    try {
      await deleteFile(imageId);
      const newImages = images.filter((_, i) => i !== index);
      onImagesChange(newImages);
      toast.success('Image removed successfully');
    } catch (error) {
      console.error('Error removing image:', error);
      toast.error('Failed to remove image');
    }
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onImagesChange(newImages);
  };

  return (
    <div className={className}>
      {/* Upload Area */}
      {images.length < maxImages && (
        <Card className="mb-4">
          <CardContent className="p-6">
            <div
              {...getRootProps()}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                isDragActive
                  ? 'border-primary bg-primary/5'
                  : 'border-muted-foreground/25 hover:border-primary/50'
              } ${uploading ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <input {...getInputProps()} />
              
              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                  <Upload className="h-6 w-6 text-muted-foreground" />
                </div>
                
                <div>
                  <p className="text-lg font-medium">
                    {isDragActive ? 'Drop images here' : 'Upload Images'}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Drag & drop images here, or click to select files
                  </p>
                </div>
                
                <div className="flex flex-wrap justify-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">JPEG, PNG, WebP, GIF</Badge>
                  <Badge variant="outline">Max {formatFileSize(maxFileSize)}</Badge>
                  <Badge variant="outline">Up to {maxImages} images</Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Progress */}
      {Object.keys(uploadProgress).length > 0 && (
        <Card className="mb-4">
          <CardContent className="p-4">
            <h4 className="font-medium mb-3">Uploading...</h4>
            <div className="space-y-2">
              {Object.entries(uploadProgress).map(([fileName, progress]) => (
                <div key={fileName} className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="truncate">{fileName}</span>
                    <span>{progress}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2">
                    <div
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((imageId, index) => (
            <Card key={imageId} className="group relative overflow-hidden">
              <CardContent className="p-0">
                <div className="relative aspect-square">
                  <Image
                    src={getFileUrl(imageId)}
                    alt={`Image ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                  
                  {/* Overlay */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <div className="flex space-x-2">
                      {index > 0 && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => moveImage(index, index - 1)}
                        >
                          ←
                        </Button>
                      )}
                      
                      {index < images.length - 1 && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => moveImage(index, index + 1)}
                        >
                          →
                        </Button>
                      )}
                      
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => removeImage(imageId, index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  
                  {/* Primary Badge */}
                  {index === 0 && (
                    <Badge className="absolute top-2 left-2">Primary</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Empty State */}
      {images.length === 0 && (
        <Card>
          <CardContent className="p-8 text-center">
            <ImageIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="font-medium mb-2">No Images</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Upload images to showcase your product
            </p>
          </CardContent>
        </Card>
      )}

      {/* Info */}
      <div className="mt-4 p-3 bg-muted/50 rounded-lg">
        <div className="flex items-start space-x-2">
          <AlertCircle className="h-4 w-4 text-muted-foreground mt-0.5 shrink-0" />
          <div className="text-sm text-muted-foreground">
            <p>• The first image will be used as the primary product image</p>
            <p>• Use the arrow buttons to reorder images</p>
            <p>• Recommended image size: 800x800px or larger</p>
            <p>• Images will be automatically optimized for web display</p>
          </div>
        </div>
      </div>
    </div>
  );
}
