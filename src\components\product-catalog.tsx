'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Search, Filter, Grid, List, Phone, Eye, SlidersHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useProducts } from '@/hooks/use-products';
import { useCategories } from '@/hooks/use-categories';
import { formatCurrency, calculateDiscountPercentage, debounce } from '@/lib/utils';
import { getFileUrl } from '@/lib/appwrite';
import type { ProductFilters } from '@/types';

export function ProductCatalog() {
  const [filters, setFilters] = useState<ProductFilters>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<string>('newest');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 12;

  const { data: productsData, loading: productsLoading, error: productsError } = useProducts(
    { ...filters, search: searchTerm },
    itemsPerPage,
    (currentPage - 1) * itemsPerPage
  );

  const { categories, loading: categoriesLoading } = useCategories();

  // Debounced search
  const debouncedSearch = debounce((term: string) => {
    setFilters(prev => ({ ...prev, search: term }));
    setCurrentPage(1);
  }, 300);

  useEffect(() => {
    debouncedSearch(searchTerm);
  }, [searchTerm, debouncedSearch]);

  const handleFilterChange = (key: keyof ProductFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm('');
    setCurrentPage(1);
  };

  const totalPages = productsData ? Math.ceil(productsData.total / itemsPerPage) : 0;

  const FilterContent = () => (
    <div className="space-y-6">
      {/* Categories */}
      <div>
        <h3 className="font-semibold mb-3">Categories</h3>
        <div className="space-y-2">
          {categories.map((category) => (
            <div key={category.$id} className="flex items-center space-x-2">
              <Checkbox
                id={`category-${category.$id}`}
                checked={filters.category === category.$id}
                onCheckedChange={(checked) => 
                  handleFilterChange('category', checked ? category.$id : undefined)
                }
              />
              <Label htmlFor={`category-${category.$id}`} className="text-sm">
                {category.name}
              </Label>
            </div>
          ))}
        </div>
      </div>

      <Separator />

      {/* Price Range */}
      <div>
        <h3 className="font-semibold mb-3">Price Range</h3>
        <div className="grid grid-cols-2 gap-2">
          <Input
            type="number"
            placeholder="Min"
            value={filters.minPrice || ''}
            onChange={(e) => handleFilterChange('minPrice', e.target.value ? Number(e.target.value) : undefined)}
          />
          <Input
            type="number"
            placeholder="Max"
            value={filters.maxPrice || ''}
            onChange={(e) => handleFilterChange('maxPrice', e.target.value ? Number(e.target.value) : undefined)}
          />
        </div>
      </div>

      <Separator />

      {/* Availability */}
      <div>
        <h3 className="font-semibold mb-3">Availability</h3>
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="in-stock"
              checked={filters.inStock === true}
              onCheckedChange={(checked) => 
                handleFilterChange('inStock', checked ? true : undefined)
              }
            />
            <Label htmlFor="in-stock" className="text-sm">In Stock Only</Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="featured"
              checked={filters.featured === true}
              onCheckedChange={(checked) => 
                handleFilterChange('featured', checked ? true : undefined)
              }
            />
            <Label htmlFor="featured" className="text-sm">Featured Products</Label>
          </div>
        </div>
      </div>

      <Separator />

      <Button variant="outline" onClick={clearFilters} className="w-full">
        Clear All Filters
      </Button>
    </div>
  );

  if (productsError) {
    return (
      <div className="text-center py-12">
        <h3 className="text-xl font-semibold mb-2">Unable to Load Products</h3>
        <p className="text-muted-foreground mb-6">
          We&apos;re experiencing technical difficulties. Please visit our store or call us for assistance.
        </p>
        <Button asChild>
          <Link href="/contact">
            <Phone className="mr-2 h-4 w-4" />
            Contact Us
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Controls */}
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search products..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Controls */}
        <div className="flex gap-2">
          {/* Mobile Filters */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="lg:hidden">
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <SheetHeader>
                <SheetTitle>Filters</SheetTitle>
              </SheetHeader>
              <div className="mt-6">
                <FilterContent />
              </div>
            </SheetContent>
          </Sheet>

          {/* Sort */}
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest First</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
              <SelectItem value="name">Name A-Z</SelectItem>
            </SelectContent>
          </Select>

          {/* View Mode */}
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="icon"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex gap-6">
        {/* Desktop Filters Sidebar */}
        <div className="hidden lg:block w-64 shrink-0">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Filter className="h-5 w-5" />
                <span>Filters</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FilterContent />
            </CardContent>
          </Card>
        </div>

        {/* Products Grid/List */}
        <div className="flex-1">
          {productsLoading ? (
            <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'}`}>
              {Array.from({ length: itemsPerPage }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader className="p-0">
                    <div className="aspect-square bg-muted rounded-t-lg" />
                  </CardHeader>
                  <CardContent className="p-4 space-y-2">
                    <div className="h-4 bg-muted rounded w-3/4" />
                    <div className="h-4 bg-muted rounded w-1/2" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : productsData && productsData.documents.length > 0 ? (
            <>
              {/* Results Info */}
              <div className="flex justify-between items-center mb-6">
                <p className="text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, productsData.total)} of {productsData.total} products
                </p>
              </div>

              {/* Products */}
              <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' : 'grid-cols-1'}`}>
                {productsData.documents.map((product) => {
                  const discountPercentage = product.compareAtPrice 
                    ? calculateDiscountPercentage(product.compareAtPrice, product.price)
                    : 0;

                  return (
                    <Card key={product.$id} className={`group hover:shadow-lg transition-shadow duration-300 ${viewMode === 'list' ? 'flex flex-row' : ''}`}>
                      <CardHeader className={`p-0 ${viewMode === 'list' ? 'w-48 shrink-0' : ''}`}>
                        <div className={`relative overflow-hidden ${viewMode === 'list' ? 'aspect-square rounded-l-lg' : 'aspect-square rounded-t-lg'}`}>
                          {product.images && product.images.length > 0 ? (
                            <Image
                              src={getFileUrl(product.images[0])}
                              alt={product.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-300"
                            />
                          ) : (
                            <div className="w-full h-full bg-muted flex items-center justify-center">
                              <Eye className="h-12 w-12 text-muted-foreground" />
                            </div>
                          )}
                          
                          {/* Badges */}
                          <div className="absolute top-3 left-3 flex flex-col gap-2">
                            {!product.inStock && (
                              <Badge variant="destructive">Out of Stock</Badge>
                            )}
                            {discountPercentage > 0 && (
                              <Badge variant="success">{discountPercentage}% Off</Badge>
                            )}
                            {product.featured && (
                              <Badge>Featured</Badge>
                            )}
                          </div>
                        </div>
                      </CardHeader>

                      <div className="flex-1 flex flex-col">
                        <CardContent className="p-4 flex-1">
                          <CardTitle className={`mb-2 ${viewMode === 'list' ? 'text-xl' : 'text-lg'} line-clamp-2`}>
                            {product.name}
                          </CardTitle>
                          
                          {product.shortDescription && (
                            <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                              {product.shortDescription}
                            </p>
                          )}

                          <div className="flex items-center gap-2 mb-3">
                            <span className="text-xl font-bold text-primary">
                              {formatCurrency(product.price)}
                            </span>
                            {product.compareAtPrice && product.compareAtPrice > product.price && (
                              <span className="text-sm text-muted-foreground line-through">
                                {formatCurrency(product.compareAtPrice)}
                              </span>
                            )}
                          </div>

                          {product.features && product.features.length > 0 && (
                            <div className="flex flex-wrap gap-1">
                              {product.features.slice(0, 3).map((feature, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                              {product.features.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{product.features.length - 3} more
                                </Badge>
                              )}
                            </div>
                          )}
                        </CardContent>

                        <CardFooter className="p-4 pt-0 flex gap-2">
                          <Button className="flex-1" asChild>
                            <Link href={`/products/${product.slug}`}>
                              View Details
                              <Eye className="ml-2 h-4 w-4" />
                            </Link>
                          </Button>
                          <Button variant="outline" asChild>
                            <Link href="/contact">
                              <Phone className="h-4 w-4" />
                            </Link>
                          </Button>
                        </CardFooter>
                      </div>
                    </Card>
                  );
                })}
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-8">
                  <Button
                    variant="outline"
                    disabled={currentPage === 1}
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  >
                    Previous
                  </Button>
                  
                  <div className="flex space-x-1">
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      const page = i + 1;
                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? 'default' : 'outline'}
                          size="icon"
                          onClick={() => setCurrentPage(page)}
                        >
                          {page}
                        </Button>
                      );
                    })}
                  </div>

                  <Button
                    variant="outline"
                    disabled={currentPage === totalPages}
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  >
                    Next
                  </Button>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-2">No Products Found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search terms or filters, or visit our store to see our full collection.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button onClick={clearFilters}>Clear Filters</Button>
                <Button variant="outline" asChild>
                  <Link href="/contact">
                    <Phone className="mr-2 h-4 w-4" />
                    Visit Store
                  </Link>
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
