'use client';

import { MapPin, Phone, Mail, Navigation, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useStoreInfo } from '@/hooks/use-store-info';
import { formatPhoneNumber } from '@/lib/utils';

export function StoreLocation() {
  const { storeInfo } = useStoreInfo();

  const getDirectionsUrl = () => {
    if (!storeInfo?.address) return '#';
    
    const address = `${storeInfo.address.street}, ${storeInfo.address.city}, ${storeInfo.address.state} ${storeInfo.address.zipCode}`;
    return `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(address)}`;
  };

  const getMapEmbedUrl = () => {
    if (!storeInfo?.address) return '';
    
    const address = `${storeInfo.address.street}, ${storeInfo.address.city}, ${storeInfo.address.state} ${storeInfo.address.zipCode}`;
    return `https://www.google.com/maps/embed/v1/place?key=YOUR_API_KEY&q=${encodeURIComponent(address)}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MapPin className="h-5 w-5 text-primary" />
          <span>Store Location</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Address */}
        <div className="space-y-2">
          <h3 className="font-semibold">Address</h3>
          {storeInfo?.address ? (
            <div className="text-muted-foreground">
              <p>{storeInfo.address.street}</p>
              <p>
                {storeInfo.address.city}, {storeInfo.address.state} {storeInfo.address.zipCode}
              </p>
              <p>{storeInfo.address.country}</p>
            </div>
          ) : (
            <div className="text-muted-foreground">
              <p>123 Main Street</p>
              <p>City, State 12345</p>
              <p>United States</p>
            </div>
          )}
        </div>

        {/* Contact Information */}
        <div className="space-y-3">
          <h3 className="font-semibold">Contact Information</h3>
          
          {storeInfo?.phone && (
            <div className="flex items-center space-x-3">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <a
                href={`tel:${storeInfo.phone}`}
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                {formatPhoneNumber(storeInfo.phone)}
              </a>
            </div>
          )}
          
          {storeInfo?.email && (
            <div className="flex items-center space-x-3">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <a
                href={`mailto:${storeInfo.email}`}
                className="text-muted-foreground hover:text-primary transition-colors"
              >
                {storeInfo.email}
              </a>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button className="flex-1" asChild>
            <a
              href={getDirectionsUrl()}
              target="_blank"
              rel="noopener noreferrer"
            >
              <Navigation className="mr-2 h-4 w-4" />
              Get Directions
            </a>
          </Button>
          
          <Button variant="outline" className="flex-1" asChild>
            <a
              href={getDirectionsUrl()}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="mr-2 h-4 w-4" />
              View on Maps
            </a>
          </Button>
        </div>

        {/* Map Placeholder */}
        <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <MapPin className="h-12 w-12 mx-auto mb-2" />
            <p className="text-sm">Interactive map will be displayed here</p>
            <p className="text-xs">Click &quot;Get Directions&quot; to view in Google Maps</p>
          </div>
        </div>

        {/* Parking Information */}
        <div className="bg-muted/50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Parking & Accessibility</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Free parking available in front of store</li>
            <li>• Wheelchair accessible entrance</li>
            <li>• Public transportation nearby</li>
            <li>• Street parking also available</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
