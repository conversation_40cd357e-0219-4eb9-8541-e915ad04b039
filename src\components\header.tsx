'use client';

import * as React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Menu, Phone, MapPin, Clock, Search, ShoppingBag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { SearchDialog } from '@/components/search-dialog';
import { useStoreInfo } from '@/hooks/use-store-info';
import { formatPhoneNumber, isStoreOpen } from '@/lib/utils';
import { cn } from '@/lib/utils';

const navigation = [
  { name: 'Home', href: '/' },
  { name: 'Products', href: '/products' },
  { name: 'About', href: '/about' },
  { name: 'Testimonials', href: '/testimonials' },
  { name: 'Contact', href: '/contact' },
];

export function Header() {
  const pathname = usePathname();
  const { storeInfo } = useStoreInfo();
  const [isOpen, setIsOpen] = React.useState(false);

  const isStoreCurrentlyOpen = storeInfo?.hours
    ? isStoreOpen(storeInfo.hours)
    : false;

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Top bar with store info */}
      <div className="border-b bg-muted/50">
        <div className="container mx-auto px-4">
          <div className="flex h-10 items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              {storeInfo?.phone && (
                <div className="flex items-center space-x-1">
                  <Phone className="h-3 w-3" />
                  <a
                    href={`tel:${storeInfo.phone}`}
                    className="hover:text-primary transition-colors"
                  >
                    {formatPhoneNumber(storeInfo.phone)}
                  </a>
                </div>
              )}
              {storeInfo?.address && (
                <div className="hidden sm:flex items-center space-x-1">
                  <MapPin className="h-3 w-3" />
                  <span className="text-muted-foreground">
                    {storeInfo.address.city}, {storeInfo.address.state}
                  </span>
                </div>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-1">
                <Clock className="h-3 w-3" />
                <Badge variant={isStoreCurrentlyOpen ? 'success' : 'secondary'}>
                  {isStoreCurrentlyOpen ? 'Open Now' : 'Closed'}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main navigation */}
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <ShoppingBag className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold">
              {storeInfo?.name || 'Salis Touch'}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'text-sm font-medium transition-colors hover:text-primary',
                  pathname === item.href
                    ? 'text-primary'
                    : 'text-muted-foreground'
                )}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <SearchDialog>
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-2" />
                Search Products
              </Button>
            </SearchDialog>
            <Button asChild>
              <Link href="/contact">Visit Store</Link>
            </Button>
          </div>

          {/* Mobile Navigation */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden">
                <Menu className="h-5 w-5" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-6">
                <Link
                  href="/"
                  className="flex items-center space-x-2 pb-4 border-b"
                  onClick={() => setIsOpen(false)}
                >
                  <ShoppingBag className="h-6 w-6 text-primary" />
                  <span className="text-lg font-bold">
                    {storeInfo?.name || 'Salis Touch'}
                  </span>
                </Link>

                <nav className="flex flex-col space-y-2">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        'px-3 py-2 rounded-md text-sm font-medium transition-colors hover:bg-accent',
                        pathname === item.href
                          ? 'bg-accent text-accent-foreground'
                          : 'text-muted-foreground hover:text-foreground'
                      )}
                      onClick={() => setIsOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </nav>

                <div className="pt-4 border-t space-y-2">
                  <SearchDialog>
                    <Button variant="outline" className="w-full">
                      <Search className="h-4 w-4 mr-2" />
                      Search Products
                    </Button>
                  </SearchDialog>
                  <Button className="w-full" asChild>
                    <Link href="/contact" onClick={() => setIsOpen(false)}>
                      Visit Store
                    </Link>
                  </Button>
                </div>

                {/* Store info in mobile menu */}
                {storeInfo && (
                  <div className="pt-4 border-t space-y-3 text-sm">
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={`tel:${storeInfo.phone}`}
                        className="hover:text-primary transition-colors"
                      >
                        {formatPhoneNumber(storeInfo.phone)}
                      </a>
                    </div>
                    {storeInfo.address && (
                      <div className="flex items-start space-x-2">
                        <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                        <div className="text-muted-foreground">
                          <div>{storeInfo.address.street}</div>
                          <div>
                            {storeInfo.address.city}, {storeInfo.address.state}{' '}
                            {storeInfo.address.zipCode}
                          </div>
                        </div>
                      </div>
                    )}
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <Badge
                        variant={isStoreCurrentlyOpen ? 'success' : 'secondary'}
                      >
                        {isStoreCurrentlyOpen ? 'Open Now' : 'Closed'}
                      </Badge>
                    </div>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
