// Base types
export interface BaseDocument {
  $id: string;
  $createdAt: string;
  $updatedAt: string;
  $permissions: string[];
  $collectionId: string;
  $databaseId: string;
}

// Product related types
export interface Product extends BaseDocument {
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  compareAtPrice?: number;
  sku?: string;
  category: string;
  categoryId: string;
  images: string[]; // Array of Appwrite file IDs
  specifications?: ProductSpecification[];
  features?: string[];
  inStock: boolean;
  stockQuantity?: number;
  featured: boolean;
  tags?: string[];
  weight?: number;
  dimensions?: ProductDimensions;
  materials?: string[];
  careInstructions?: string;
  seoTitle?: string;
  seoDescription?: string;
  slug: string;
}

export interface ProductSpecification {
  name: string;
  value: string;
}

export interface ProductDimensions {
  length?: number;
  width?: number;
  height?: number;
  unit: 'cm' | 'in';
}

export interface Category extends BaseDocument {
  name: string;
  description?: string;
  slug: string;
  image?: string; // Appwrite file ID
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
  seoTitle?: string;
  seoDescription?: string;
}

// Store information types
export interface StoreInfo extends BaseDocument {
  name: string;
  description: string;
  phone: string;
  email: string;
  address: StoreAddress;
  hours: StoreHours[];
  socialMedia?: SocialMediaLinks;
  announcements?: Announcement[];
  policies?: StorePolicy[];
  aboutUs?: string;
  mission?: string;
  vision?: string;
  teamMembers?: TeamMember[];
}

export interface StoreAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface StoreHours {
  dayOfWeek: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  openTime: string; // Format: "09:00"
  closeTime: string; // Format: "18:00"
  isClosed: boolean;
}

export interface SocialMediaLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  youtube?: string;
  tiktok?: string;
}

export interface Announcement {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'success' | 'error';
  isActive: boolean;
  startDate?: string;
  endDate?: string;
}

export interface StorePolicy {
  id: string;
  title: string;
  content: string;
  type: 'return' | 'privacy' | 'terms' | 'shipping' | 'other';
  lastUpdated: string;
}

export interface TeamMember {
  id: string;
  name: string;
  position: string;
  bio?: string;
  image?: string; // Appwrite file ID
  email?: string;
  phone?: string;
}

// Testimonial types
export interface Testimonial extends BaseDocument {
  customerName: string;
  customerEmail?: string;
  customerImage?: string; // Appwrite file ID
  rating: number; // 1-5
  title?: string;
  content: string;
  productId?: string;
  isApproved: boolean;
  isFeatured: boolean;
  location?: string;
  purchaseDate?: string;
}

// Inquiry types
export interface Inquiry extends BaseDocument {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  type: 'general' | 'product' | 'appointment' | 'complaint' | 'other';
  productId?: string;
  status: 'new' | 'in-progress' | 'resolved' | 'closed';
  priority: 'low' | 'medium' | 'high';
  assignedTo?: string;
  notes?: InquiryNote[];
  source: 'website' | 'phone' | 'email' | 'in-person';
}

export interface InquiryNote {
  id: string;
  content: string;
  createdAt: string;
  createdBy: string;
}

// Analytics types
export interface AnalyticsEvent extends BaseDocument {
  type: 'page_view' | 'product_view' | 'contact_form' | 'phone_click' | 'direction_click';
  page?: string;
  productId?: string;
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  documents: T[];
  total: number;
  limit: number;
  offset: number;
}

// Form types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  type: Inquiry['type'];
  productId?: string;
}

export interface ProductFormData {
  name: string;
  description: string;
  shortDescription?: string;
  price: number;
  compareAtPrice?: number;
  sku?: string;
  categoryId: string;
  specifications?: ProductSpecification[];
  features?: string[];
  inStock: boolean;
  stockQuantity?: number;
  featured: boolean;
  tags?: string[];
  weight?: number;
  dimensions?: ProductDimensions;
  materials?: string[];
  careInstructions?: string;
  seoTitle?: string;
  seoDescription?: string;
}

// Search and filter types
export interface ProductFilters {
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  featured?: boolean;
  tags?: string[];
  search?: string;
}

export interface SearchResult<T> {
  items: T[];
  total: number;
  query: string;
  filters?: Record<string, any>;
}
