import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ProductDetail } from '@/components/product-detail';
import {
  ProductStructuredData,
  BreadcrumbStructuredData,
} from '@/components/seo/structured-data';
import { productService, storeInfoService } from '@/lib/database';

interface ProductPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({
  params,
}: ProductPageProps): Promise<Metadata> {
  try {
    const resolvedParams = await params;
    const product = await productService.getProductBySlug(resolvedParams.slug);

    if (!product) {
      return {
        title: 'Product Not Found - Salis Touch',
        description: 'The requested product could not be found.',
      };
    }

    return {
      title: `${product.name} - Salis Touch`,
      description:
        product.seoDescription ||
        product.shortDescription ||
        product.description.substring(0, 160),
      keywords: product.tags?.join(', ') || 'product, quality, handcrafted',
      openGraph: {
        title: product.name,
        description:
          product.shortDescription || product.description.substring(0, 160),
        type: 'website',
        images:
          product.images?.length > 0
            ? [
                {
                  url: `${process.env.NEXT_PUBLIC_APPWRITE_ENDPOINT}/storage/buckets/${process.env.NEXT_PUBLIC_APPWRITE_STORAGE_BUCKET_ID}/files/${product.images[0]}/view?project=${process.env.NEXT_PUBLIC_APPWRITE_PROJECT_ID}`,
                  width: 800,
                  height: 600,
                  alt: product.name,
                },
              ]
            : [],
      },
      twitter: {
        card: 'summary_large_image',
        title: product.name,
        description:
          product.shortDescription || product.description.substring(0, 160),
      },
    };
  } catch (error) {
    return {
      title: 'Product Not Found - Salis Touch',
      description: 'The requested product could not be found.',
    };
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  const resolvedParams = await params;
  const product = await productService.getProductBySlug(resolvedParams.slug);

  if (!product) {
    notFound();
  }

  const storeInfo = await storeInfoService.getStoreInfo();

  const breadcrumbItems = [
    { name: 'Home', url: process.env.NEXT_PUBLIC_SITE_URL || '' },
    { name: 'Products', url: `${process.env.NEXT_PUBLIC_SITE_URL}/products` },
    {
      name: product.category,
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/products?category=${product.categoryId}`,
    },
    {
      name: product.name,
      url: `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.slug}`,
    },
  ];

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <ProductDetail product={product} />
      </main>
      <Footer />
      <ProductStructuredData product={product} storeInfo={storeInfo} />
      <BreadcrumbStructuredData items={breadcrumbItems} />
    </div>
  );
}
