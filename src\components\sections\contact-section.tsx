'use client';

import Link from 'next/link';
import { Phone, Mail, MapPin, Clock, ArrowRight, Calendar } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useStoreInfo } from '@/hooks/use-store-info';
import { formatPhoneNumber, isStoreOpen, getNextOpeningTime } from '@/lib/utils';

const daysOfWeek = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];

export function ContactSection() {
  const { storeInfo } = useStoreInfo();
  const isStoreCurrentlyOpen = storeInfo?.hours ? isStoreOpen(storeInfo.hours) : false;
  const nextOpeningTime = storeInfo?.hours ? getNextOpeningTime(storeInfo.hours) : null;

  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Visit Us Today</h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Experience our products in person and get expert advice from our knowledgeable team. 
            We&apos;re here to help you find exactly what you&apos;re looking for.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Contact Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MapPin className="h-5 w-5 text-primary" />
                  <span>Store Location</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {storeInfo?.address ? (
                  <div className="space-y-2">
                    <p className="font-medium">{storeInfo.address.street}</p>
                    <p className="text-muted-foreground">
                      {storeInfo.address.city}, {storeInfo.address.state} {storeInfo.address.zipCode}
                    </p>
                    <p className="text-muted-foreground">{storeInfo.address.country}</p>
                  </div>
                ) : (
                  <p className="text-muted-foreground">
                    123 Main Street<br />
                    City, State 12345<br />
                    United States
                  </p>
                )}
                
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button className="flex-1" asChild>
                    <Link href="/contact">
                      <MapPin className="mr-2 h-4 w-4" />
                      Get Directions
                    </Link>
                  </Button>
                  <Button variant="outline" className="flex-1" asChild>
                    <Link href="/contact#location">
                      <Calendar className="mr-2 h-4 w-4" />
                      Plan Visit
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Phone className="h-5 w-5 text-primary" />
                  <span>Contact Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  {storeInfo?.phone && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Phone:</span>
                      <a
                        href={`tel:${storeInfo.phone}`}
                        className="font-medium hover:text-primary transition-colors"
                      >
                        {formatPhoneNumber(storeInfo.phone)}
                      </a>
                    </div>
                  )}
                  
                  {storeInfo?.email && (
                    <div className="flex items-center justify-between">
                      <span className="text-muted-foreground">Email:</span>
                      <a
                        href={`mailto:${storeInfo.email}`}
                        className="font-medium hover:text-primary transition-colors"
                      >
                        {storeInfo.email}
                      </a>
                    </div>
                  )}
                </div>

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button className="flex-1" asChild>
                    <a href={`tel:${storeInfo?.phone || '******-0123'}`}>
                      <Phone className="mr-2 h-4 w-4" />
                      Call Now
                    </a>
                  </Button>
                  <Button variant="outline" className="flex-1" asChild>
                    <a href={`mailto:${storeInfo?.email || '<EMAIL>'}`}>
                      <Mail className="mr-2 h-4 w-4" />
                      Send Email
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Store Hours */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-5 w-5 text-primary" />
                    <span>Store Hours</span>
                  </div>
                  <Badge variant={isStoreCurrentlyOpen ? 'success' : 'secondary'}>
                    {isStoreCurrentlyOpen ? 'Open Now' : 'Closed'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {storeInfo?.hours && storeInfo.hours.length > 0 ? (
                  <div className="space-y-2">
                    {daysOfWeek.map((day) => {
                      const dayHours = storeInfo.hours.find(
                        h => h.dayOfWeek.toLowerCase() === day.toLowerCase()
                      );
                      
                      return (
                        <div key={day} className="flex justify-between items-center">
                          <span className="text-muted-foreground">{day}:</span>
                          <span className="font-medium">
                            {dayHours?.isClosed 
                              ? 'Closed' 
                              : `${dayHours?.openTime || '9:00'} - ${dayHours?.closeTime || '18:00'}`
                            }
                          </span>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Monday - Friday:</span>
                      <span className="font-medium">9:00 AM - 6:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Saturday:</span>
                      <span className="font-medium">10:00 AM - 5:00 PM</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Sunday:</span>
                      <span className="font-medium">Closed</span>
                    </div>
                  </div>
                )}

                {!isStoreCurrentlyOpen && nextOpeningTime && (
                  <div className="mt-4 p-3 bg-muted rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      <strong>Next opening:</strong> {nextOpeningTime}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full" size="lg" asChild>
                  <Link href="/contact">
                    <MapPin className="mr-2 h-5 w-5" />
                    Visit Our Store
                  </Link>
                </Button>
                
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/products">
                    Browse Products
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>

                <Button variant="outline" className="w-full" asChild>
                  <Link href="/contact#inquiry">
                    Send Inquiry
                    <Mail className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Special Announcements */}
        {storeInfo?.announcements && storeInfo.announcements.length > 0 && (
          <div className="mt-12">
            <h3 className="text-xl font-semibold mb-6 text-center">Store Announcements</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {storeInfo.announcements
                .filter(announcement => announcement.isActive)
                .slice(0, 2)
                .map((announcement) => (
                  <Card key={announcement.id} className="border-l-4 border-l-primary">
                    <CardContent className="p-4">
                      <h4 className="font-semibold mb-2">{announcement.title}</h4>
                      <p className="text-sm text-muted-foreground">{announcement.message}</p>
                    </CardContent>
                  </Card>
                ))}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
