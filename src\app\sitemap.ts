import { MetadataRoute } from 'next';
import { productService, categoryService } from '@/lib/database';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://salistouch.com';

  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/products`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/testimonials`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    },
  ];

  try {
    // Get all products
    const productsResponse = await productService.getProducts({}, 1000);
    const productPages = productsResponse.documents.map((product) => ({
      url: `${baseUrl}/products/${product.slug}`,
      lastModified: new Date(product.$updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    }));

    // Get all categories
    const categories = await categoryService.getCategories();
    const categoryPages = categories.map((category) => ({
      url: `${baseUrl}/products?category=${category.$id}`,
      lastModified: new Date(category.$updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.5,
    }));

    return [...staticPages, ...productPages, ...categoryPages];
  } catch (error) {
    console.error('Error generating sitemap:', error);
    // Return static pages only if there's an error
    return staticPages;
  }
}
