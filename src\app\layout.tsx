import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { ThemeProvider } from 'next-themes';
import { Toaster } from '@/components/ui/sonner';
import {
  WebsiteStructuredData,
  OrganizationStructuredData,
} from '@/components/seo/structured-data';
import './globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Salis Touch - Premium Products & Services',
  description:
    'Discover our premium collection of products and services. Visit our store for personalized assistance and expert recommendations.',
  keywords:
    'premium products, quality services, local business, handcrafted, artisan',
  authors: [{ name: 'Salis Touch' }],
  creator: 'Sal<PERSON>',
  publisher: 'Salis Touch',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: process.env.NEXT_PUBLIC_SITE_URL,
    siteName: 'Salis Touch',
    title: 'Salis Touch - Premium Products & Services',
    description:
      'Discover our premium collection of products and services. Visit our store for personalized assistance and expert recommendations.',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Salis Touch - Premium Products & Services',
    description:
      'Discover our premium collection of products and services. Visit our store for personalized assistance and expert recommendations.',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
          <Toaster />
          <WebsiteStructuredData />
          <OrganizationStructuredData />
        </ThemeProvider>
      </body>
    </html>
  );
}
