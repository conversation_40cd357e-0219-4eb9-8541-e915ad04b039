import { databases, DATABASE_ID, COLLECTION_IDS, Query } from './appwrite';
import type {
  Product,
  Category,
  Testimonial,
  Inquiry,
  StoreInfo,
  AnalyticsEvent,
  ProductFilters,
  PaginatedResponse,
} from '@/types';

// Generic database operations
class DatabaseService {
  // Generic CRUD operations
  async create<T>(collectionId: string, data: Omit<T, keyof import('@/types').BaseDocument>): Promise<T> {
    try {
      const response = await databases.createDocument(
        DATABASE_ID,
        collectionId,
        'unique()',
        data
      );
      return response as T;
    } catch (error) {
      console.error(`Error creating document in ${collectionId}:`, error);
      throw new Error(`Failed to create document`);
    }
  }

  async getById<T>(collectionId: string, id: string): Promise<T | null> {
    try {
      const response = await databases.getDocument(DATABASE_ID, collectionId, id);
      return response as T;
    } catch (error) {
      console.error(`Error getting document ${id} from ${collectionId}:`, error);
      return null;
    }
  }

  async update<T>(collectionId: string, id: string, data: Partial<T>): Promise<T> {
    try {
      const response = await databases.updateDocument(
        DATABASE_ID,
        collectionId,
        id,
        data
      );
      return response as T;
    } catch (error) {
      console.error(`Error updating document ${id} in ${collectionId}:`, error);
      throw new Error(`Failed to update document`);
    }
  }

  async delete(collectionId: string, id: string): Promise<void> {
    try {
      await databases.deleteDocument(DATABASE_ID, collectionId, id);
    } catch (error) {
      console.error(`Error deleting document ${id} from ${collectionId}:`, error);
      throw new Error(`Failed to delete document`);
    }
  }

  async list<T>(
    collectionId: string,
    queries: string[] = [],
    limit: number = 25,
    offset: number = 0
  ): Promise<PaginatedResponse<T>> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        collectionId,
        [
          Query.limit(limit),
          Query.offset(offset),
          ...queries,
        ]
      );
      
      return {
        documents: response.documents as T[],
        total: response.total,
        limit,
        offset,
      };
    } catch (error) {
      console.error(`Error listing documents from ${collectionId}:`, error);
      throw new Error(`Failed to list documents`);
    }
  }
}

// Product service
export class ProductService extends DatabaseService {
  async getProducts(filters?: ProductFilters, limit = 25, offset = 0): Promise<PaginatedResponse<Product>> {
    const queries: string[] = [];

    if (filters?.category) {
      queries.push(Query.equal('categoryId', filters.category));
    }
    if (filters?.inStock !== undefined) {
      queries.push(Query.equal('inStock', filters.inStock));
    }
    if (filters?.featured !== undefined) {
      queries.push(Query.equal('featured', filters.featured));
    }
    if (filters?.minPrice !== undefined) {
      queries.push(Query.greaterThanEqual('price', filters.minPrice));
    }
    if (filters?.maxPrice !== undefined) {
      queries.push(Query.lessThanEqual('price', filters.maxPrice));
    }
    if (filters?.search) {
      queries.push(Query.search('name', filters.search));
    }

    queries.push(Query.orderDesc('$createdAt'));

    return this.list<Product>(COLLECTION_IDS.PRODUCTS, queries, limit, offset);
  }

  async getProductBySlug(slug: string): Promise<Product | null> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_IDS.PRODUCTS,
        [Query.equal('slug', slug), Query.limit(1)]
      );
      
      return response.documents.length > 0 ? response.documents[0] as Product : null;
    } catch (error) {
      console.error(`Error getting product by slug ${slug}:`, error);
      return null;
    }
  }

  async getFeaturedProducts(limit = 8): Promise<Product[]> {
    const response = await this.list<Product>(
      COLLECTION_IDS.PRODUCTS,
      [Query.equal('featured', true), Query.equal('inStock', true)],
      limit
    );
    return response.documents;
  }

  async getRelatedProducts(categoryId: string, excludeId: string, limit = 4): Promise<Product[]> {
    const response = await this.list<Product>(
      COLLECTION_IDS.PRODUCTS,
      [
        Query.equal('categoryId', categoryId),
        Query.notEqual('$id', excludeId),
        Query.equal('inStock', true),
      ],
      limit
    );
    return response.documents;
  }

  async createProduct(data: Omit<Product, keyof import('@/types').BaseDocument>): Promise<Product> {
    return this.create<Product>(COLLECTION_IDS.PRODUCTS, data);
  }

  async updateProduct(id: string, data: Partial<Product>): Promise<Product> {
    return this.update<Product>(COLLECTION_IDS.PRODUCTS, id, data);
  }

  async deleteProduct(id: string): Promise<void> {
    return this.delete(COLLECTION_IDS.PRODUCTS, id);
  }
}

// Category service
export class CategoryService extends DatabaseService {
  async getCategories(): Promise<Category[]> {
    const response = await this.list<Category>(
      COLLECTION_IDS.CATEGORIES,
      [Query.equal('isActive', true), Query.orderAsc('sortOrder')]
    );
    return response.documents;
  }

  async getCategoryBySlug(slug: string): Promise<Category | null> {
    try {
      const response = await databases.listDocuments(
        DATABASE_ID,
        COLLECTION_IDS.CATEGORIES,
        [Query.equal('slug', slug), Query.limit(1)]
      );
      
      return response.documents.length > 0 ? response.documents[0] as Category : null;
    } catch (error) {
      console.error(`Error getting category by slug ${slug}:`, error);
      return null;
    }
  }

  async createCategory(data: Omit<Category, keyof import('@/types').BaseDocument>): Promise<Category> {
    return this.create<Category>(COLLECTION_IDS.CATEGORIES, data);
  }

  async updateCategory(id: string, data: Partial<Category>): Promise<Category> {
    return this.update<Category>(COLLECTION_IDS.CATEGORIES, id, data);
  }

  async deleteCategory(id: string): Promise<void> {
    return this.delete(COLLECTION_IDS.CATEGORIES, id);
  }
}

// Testimonial service
export class TestimonialService extends DatabaseService {
  async getApprovedTestimonials(limit = 10): Promise<Testimonial[]> {
    const response = await this.list<Testimonial>(
      COLLECTION_IDS.TESTIMONIALS,
      [Query.equal('isApproved', true), Query.orderDesc('$createdAt')],
      limit
    );
    return response.documents;
  }

  async getFeaturedTestimonials(limit = 6): Promise<Testimonial[]> {
    const response = await this.list<Testimonial>(
      COLLECTION_IDS.TESTIMONIALS,
      [
        Query.equal('isApproved', true),
        Query.equal('isFeatured', true),
        Query.orderDesc('$createdAt'),
      ],
      limit
    );
    return response.documents;
  }

  async createTestimonial(data: Omit<Testimonial, keyof import('@/types').BaseDocument>): Promise<Testimonial> {
    return this.create<Testimonial>(COLLECTION_IDS.TESTIMONIALS, data);
  }

  async updateTestimonial(id: string, data: Partial<Testimonial>): Promise<Testimonial> {
    return this.update<Testimonial>(COLLECTION_IDS.TESTIMONIALS, id, data);
  }
}

// Inquiry service
export class InquiryService extends DatabaseService {
  async createInquiry(data: Omit<Inquiry, keyof import('@/types').BaseDocument>): Promise<Inquiry> {
    return this.create<Inquiry>(COLLECTION_IDS.INQUIRIES, data);
  }

  async getInquiries(status?: Inquiry['status'], limit = 25, offset = 0): Promise<PaginatedResponse<Inquiry>> {
    const queries: string[] = [Query.orderDesc('$createdAt')];
    
    if (status) {
      queries.push(Query.equal('status', status));
    }

    return this.list<Inquiry>(COLLECTION_IDS.INQUIRIES, queries, limit, offset);
  }

  async updateInquiry(id: string, data: Partial<Inquiry>): Promise<Inquiry> {
    return this.update<Inquiry>(COLLECTION_IDS.INQUIRIES, id, data);
  }
}

// Store info service
export class StoreInfoService extends DatabaseService {
  async getStoreInfo(): Promise<StoreInfo | null> {
    try {
      const response = await this.list<StoreInfo>(COLLECTION_IDS.STORE_INFO, [], 1);
      return response.documents.length > 0 ? response.documents[0] : null;
    } catch (error) {
      console.error('Error getting store info:', error);
      return null;
    }
  }

  async updateStoreInfo(id: string, data: Partial<StoreInfo>): Promise<StoreInfo> {
    return this.update<StoreInfo>(COLLECTION_IDS.STORE_INFO, id, data);
  }

  async createStoreInfo(data: Omit<StoreInfo, keyof import('@/types').BaseDocument>): Promise<StoreInfo> {
    return this.create<StoreInfo>(COLLECTION_IDS.STORE_INFO, data);
  }
}

// Analytics service
export class AnalyticsService extends DatabaseService {
  async trackEvent(data: Omit<AnalyticsEvent, keyof import('@/types').BaseDocument>): Promise<void> {
    try {
      await this.create<AnalyticsEvent>(COLLECTION_IDS.ANALYTICS, data);
    } catch (error) {
      // Don't throw errors for analytics to avoid breaking user experience
      console.error('Error tracking analytics event:', error);
    }
  }

  async getAnalytics(
    type?: AnalyticsEvent['type'],
    startDate?: string,
    endDate?: string,
    limit = 100
  ): Promise<AnalyticsEvent[]> {
    const queries: string[] = [Query.orderDesc('$createdAt')];
    
    if (type) {
      queries.push(Query.equal('type', type));
    }
    if (startDate) {
      queries.push(Query.greaterThanEqual('$createdAt', startDate));
    }
    if (endDate) {
      queries.push(Query.lessThanEqual('$createdAt', endDate));
    }

    const response = await this.list<AnalyticsEvent>(COLLECTION_IDS.ANALYTICS, queries, limit);
    return response.documents;
  }
}

// Export service instances
export const productService = new ProductService();
export const categoryService = new CategoryService();
export const testimonialService = new TestimonialService();
export const inquiryService = new InquiryService();
export const storeInfoService = new StoreInfoService();
export const analyticsService = new AnalyticsService();
