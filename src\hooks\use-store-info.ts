'use client';

import { useState, useEffect } from 'react';
import { storeInfoService } from '@/lib/database';
import type { StoreInfo } from '@/types';

export function useStoreInfo() {
  const [storeInfo, setStoreInfo] = useState<StoreInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStoreInfo = async () => {
      try {
        setLoading(true);
        setError(null);
        const result = await storeInfoService.getStoreInfo();
        setStoreInfo(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch store information');
      } finally {
        setLoading(false);
      }
    };

    fetchStoreInfo();
  }, []);

  return { storeInfo, loading, error, refetch: () => setLoading(true) };
}
