'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Star, Quote, User, Calendar, MapPin, ArrowRight, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { testimonialService } from '@/lib/database';
import { formatDate } from '@/lib/utils';
import type { Testimonial } from '@/types';
import Image from 'next/image';

export function TestimonialsContent() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | '5' | '4' | '3'>('all');

  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const result = await testimonialService.getApprovedTestimonials(50);
        setTestimonials(result);
      } catch (error) {
        console.error('Error fetching testimonials:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }).map((_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-muted-foreground'
        }`}
      />
    ));
  };

  const filteredTestimonials = testimonials.filter(testimonial => {
    if (filter === 'all') return true;
    return testimonial.rating >= parseInt(filter);
  });

  const averageRating = testimonials.length > 0 
    ? testimonials.reduce((sum, t) => sum + t.rating, 0) / testimonials.length 
    : 0;

  const ratingCounts = {
    5: testimonials.filter(t => t.rating === 5).length,
    4: testimonials.filter(t => t.rating === 4).length,
    3: testimonials.filter(t => t.rating === 3).length,
    2: testimonials.filter(t => t.rating === 2).length,
    1: testimonials.filter(t => t.rating === 1).length,
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Customer Testimonials</h1>
          <p className="text-xl text-muted-foreground">Loading customer reviews...</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6 space-y-4">
                <div className="h-4 bg-muted rounded w-3/4" />
                <div className="h-4 bg-muted rounded w-full" />
                <div className="h-4 bg-muted rounded w-2/3" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold mb-6">Customer Testimonials</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Don&apos;t just take our word for it. Here&apos;s what our valued customers have to say about their experience with us.
        </p>
      </div>

      {/* Statistics */}
      {testimonials.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-primary mb-2">
                {averageRating.toFixed(1)}
              </div>
              <div className="flex justify-center mb-2">
                {renderStars(Math.round(averageRating))}
              </div>
              <p className="text-sm text-muted-foreground">Average Rating</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-primary mb-2">
                {testimonials.length}
              </div>
              <p className="text-sm text-muted-foreground">Total Reviews</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-primary mb-2">
                {ratingCounts[5]}
              </div>
              <p className="text-sm text-muted-foreground">5-Star Reviews</p>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="p-6">
              <div className="text-3xl font-bold text-primary mb-2">
                {Math.round((ratingCounts[5] / testimonials.length) * 100)}%
              </div>
              <p className="text-sm text-muted-foreground">Recommend Us</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filter */}
      {testimonials.length > 0 && (
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-2xl font-semibold">
            Customer Reviews ({filteredTestimonials.length})
          </h2>
          <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Reviews</SelectItem>
              <SelectItem value="5">5 Stars Only</SelectItem>
              <SelectItem value="4">4+ Stars</SelectItem>
              <SelectItem value="3">3+ Stars</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      {/* Testimonials Grid */}
      {filteredTestimonials.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {filteredTestimonials.map((testimonial) => (
            <Card key={testimonial.$id} className="hover:shadow-lg transition-shadow duration-300">
              <CardContent className="p-6 space-y-4">
                {/* Quote Icon and Rating */}
                <div className="flex justify-between items-start">
                  <Quote className="h-8 w-8 text-primary/20" />
                  <div className="flex items-center space-x-1">
                    {renderStars(testimonial.rating)}
                  </div>
                </div>

                {/* Title */}
                {testimonial.title && (
                  <h3 className="font-semibold text-lg">{testimonial.title}</h3>
                )}

                {/* Content */}
                <blockquote className="text-muted-foreground leading-relaxed">
                  &quot;{testimonial.content}&quot;
                </blockquote>

                {/* Customer Info */}
                <div className="flex items-center space-x-3 pt-4 border-t">
                  <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                    {testimonial.customerImage ? (
                      <Image
                        src={testimonial.customerImage}
                        alt={testimonial.customerName}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User className="h-5 w-5 text-muted-foreground" />
                    )}
                  </div>
                  <div className="flex-1">
                    <p className="font-medium">{testimonial.customerName}</p>
                    <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                      {testimonial.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{testimonial.location}</span>
                        </div>
                      )}
                      {testimonial.purchaseDate && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{formatDate(testimonial.purchaseDate)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Badges */}
                <div className="flex justify-between items-center">
                  <Badge variant="outline" className="text-xs">
                    Verified Customer
                  </Badge>
                  {testimonial.isFeatured && (
                    <Badge className="text-xs">Featured Review</Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : testimonials.length === 0 ? (
        <div className="text-center py-12">
          <MessageSquare className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-2">No Reviews Yet</h3>
          <p className="text-muted-foreground mb-6">
            Be the first to share your experience with us! We&apos;d love to hear from you.
          </p>
          <Button asChild>
            <Link href="#write-review">Write the First Review</Link>
          </Button>
        </div>
      ) : (
        <div className="text-center py-12">
          <h3 className="text-xl font-semibold mb-2">No Reviews Match Your Filter</h3>
          <p className="text-muted-foreground mb-6">
            Try adjusting your filter to see more reviews.
          </p>
          <Button onClick={() => setFilter('all')} variant="outline">
            Show All Reviews
          </Button>
        </div>
      )}

      {/* Write Review Section */}
      <div id="write-review" className="bg-primary/5 rounded-lg p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Share Your Experience</h2>
        <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
          We value your feedback! Share your experience with our products and services to help other customers 
          and help us continue to improve.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link href="/contact">
              <MessageSquare className="mr-2 h-5 w-5" />
              Write a Review
            </Link>
          </Button>
          <Button size="lg" variant="outline" asChild>
            <Link href="/contact">
              Contact Us
              <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </div>

      {/* Call to Action */}
      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold mb-4">Ready to Experience It Yourself?</h2>
        <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
          Join our satisfied customers and discover why they keep coming back. Visit our store today!
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button size="lg" asChild>
            <Link href="/contact">Visit Our Store</Link>
          </Button>
          <Button size="lg" variant="outline" asChild>
            <Link href="/products">Browse Products</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
