import { Metada<PERSON> } from 'next';
import { Header } from '@/components/header';
import { Footer } from '@/components/footer';
import { ProductCatalog } from '@/components/product-catalog';

export const metadata: Metadata = {
  title: 'Products - Salis Touch',
  description: 'Browse our complete collection of premium products. Find exactly what you\'re looking for with our easy-to-use catalog and filters.',
  keywords: 'products, catalog, browse, shop, premium, quality',
};

export default function ProductsPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8">
          <div className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">Our Products</h1>
            <p className="text-lg text-muted-foreground max-w-2xl">
              Discover our carefully curated collection of premium products. Each item is selected for its quality, 
              craftsmanship, and value. Visit our store to see these products in person and get expert recommendations.
            </p>
          </div>
          <ProductCatalog />
        </div>
      </main>
      <Footer />
    </div>
  );
}
