'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  BarChart3, 
  Package, 
  MessageSquare, 
  Settings, 
  Users, 
  Star,
  TrendingUp,
  Eye,
  Phone,
  LogOut
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAdmin } from './admin-provider';
import { productService, inquiryService, testimonialService, analyticsService } from '@/lib/database';

interface DashboardStats {
  totalProducts: number;
  inStockProducts: number;
  featuredProducts: number;
  newInquiries: number;
  totalInquiries: number;
  approvedTestimonials: number;
  pendingTestimonials: number;
  todayViews: number;
}

export function AdminDashboard() {
  const { logout } = useAdmin();
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    inStockProducts: 0,
    featuredProducts: 0,
    newInquiries: 0,
    totalInquiries: 0,
    approvedTestimonials: 0,
    pendingTestimonials: 0,
    todayViews: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Fetch products stats
        const productsData = await productService.getProducts({}, 1000);
        const totalProducts = productsData.total;
        const inStockProducts = productsData.documents.filter(p => p.inStock).length;
        const featuredProducts = productsData.documents.filter(p => p.featured).length;

        // Fetch inquiries stats
        const inquiriesData = await inquiryService.getInquiries(undefined, 1000);
        const totalInquiries = inquiriesData.total;
        const newInquiries = inquiriesData.documents.filter(i => i.status === 'new').length;

        // Fetch testimonials stats
        const allTestimonials = await testimonialService.list('testimonials', [], 1000);
        const approvedTestimonials = allTestimonials.documents.filter((t: any) => t.isApproved).length;
        const pendingTestimonials = allTestimonials.documents.filter((t: any) => !t.isApproved).length;

        // Fetch today's analytics
        const today = new Date().toISOString().split('T')[0];
        const todayAnalytics = await analyticsService.getAnalytics('page_view', today);
        const todayViews = todayAnalytics.length;

        setStats({
          totalProducts,
          inStockProducts,
          featuredProducts,
          newInquiries,
          totalInquiries,
          approvedTestimonials,
          pendingTestimonials,
          todayViews,
        });
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const quickActions = [
    {
      title: 'Manage Products',
      description: 'Add, edit, or remove products from your catalog',
      icon: Package,
      href: '/admin/products',
      color: 'bg-blue-500',
    },
    {
      title: 'Customer Inquiries',
      description: 'Respond to customer questions and requests',
      icon: MessageSquare,
      href: '/admin/inquiries',
      color: 'bg-green-500',
      badge: stats.newInquiries > 0 ? stats.newInquiries : undefined,
    },
    {
      title: 'Testimonials',
      description: 'Review and approve customer testimonials',
      icon: Star,
      href: '/admin/testimonials',
      color: 'bg-yellow-500',
      badge: stats.pendingTestimonials > 0 ? stats.pendingTestimonials : undefined,
    },
    {
      title: 'Analytics',
      description: 'View website traffic and performance metrics',
      icon: BarChart3,
      href: '/admin/analytics',
      color: 'bg-purple-500',
    },
    {
      title: 'Store Settings',
      description: 'Update store information, hours, and policies',
      icon: Settings,
      href: '/admin/settings',
      color: 'bg-gray-500',
    },
  ];

  return (
    <div className="min-h-screen bg-muted/30">
      {/* Header */}
      <header className="bg-background border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold">Admin Dashboard</h1>
              <p className="text-muted-foreground">Manage your store and customer interactions</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" asChild>
                <Link href="/" target="_blank">
                  <Eye className="mr-2 h-4 w-4" />
                  View Store
                </Link>
              </Button>
              <Button variant="outline" onClick={logout}>
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Products</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? '...' : stats.totalProducts}</div>
              <p className="text-xs text-muted-foreground">
                {loading ? '...' : stats.inStockProducts} in stock
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Customer Inquiries</CardTitle>
              <MessageSquare className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? '...' : stats.totalInquiries}</div>
              <p className="text-xs text-muted-foreground">
                {loading ? '...' : stats.newInquiries} new inquiries
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Testimonials</CardTitle>
              <Star className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? '...' : stats.approvedTestimonials}</div>
              <p className="text-xs text-muted-foreground">
                {loading ? '...' : stats.pendingTestimonials} pending approval
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Today&apos;s Views</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{loading ? '...' : stats.todayViews}</div>
              <p className="text-xs text-muted-foreground">
                Page views today
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer">
                <Link href={action.href}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className={`p-3 rounded-lg ${action.color}`}>
                        <action.icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold">{action.title}</h3>
                          {action.badge && (
                            <Badge variant="destructive">{action.badge}</Badge>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Link>
              </Card>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Inquiries</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-center text-muted-foreground py-8">
                  <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No recent inquiries</p>
                  <p className="text-sm">New customer inquiries will appear here</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Quick Stats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Featured Products</span>
                  <span className="font-medium">{loading ? '...' : stats.featuredProducts}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Products in Stock</span>
                  <span className="font-medium">{loading ? '...' : stats.inStockProducts}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Approved Reviews</span>
                  <span className="font-medium">{loading ? '...' : stats.approvedTestimonials}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Pending Reviews</span>
                  <span className="font-medium">{loading ? '...' : stats.pendingTestimonials}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Help Section */}
        <Card className="mt-8">
          <CardContent className="p-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Need Help?</h3>
              <p className="text-muted-foreground mb-4">
                Get started with managing your store or contact support for assistance.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button variant="outline">
                  <Users className="mr-2 h-4 w-4" />
                  View Documentation
                </Button>
                <Button variant="outline">
                  <Phone className="mr-2 h-4 w-4" />
                  Contact Support
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
