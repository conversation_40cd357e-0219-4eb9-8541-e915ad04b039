'use client';

import Link from 'next/link';
import { ArrowRight, Phone, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useStoreInfo } from '@/hooks/use-store-info';
import { formatPhoneNumber, isStoreOpen } from '@/lib/utils';

export function HeroSection() {
  const { storeInfo } = useStoreInfo();
  const isStoreCurrentlyOpen = storeInfo?.hours ? isStoreOpen(storeInfo.hours) : false;

  return (
    <section className="relative bg-gradient-to-br from-primary/5 via-background to-secondary/5 py-20 lg:py-32">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center space-y-8">
          {/* Store Status Badge */}
          <div className="flex justify-center">
            <Badge 
              variant={isStoreCurrentlyOpen ? 'success' : 'secondary'}
              className="text-sm px-4 py-2"
            >
              {isStoreCurrentlyOpen ? '🟢 Open Now - Visit Us Today!' : '🔴 Currently Closed'}
            </Badge>
          </div>

          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
              Welcome to{' '}
              <span className="text-primary">
                {storeInfo?.name || 'Salis Touch'}
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {storeInfo?.description || 
                'Discover our premium collection of handcrafted products and personalized services. Experience quality that speaks for itself.'}
            </p>
          </div>

          {/* Call-to-Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="text-lg px-8 py-6" asChild>
              <Link href="/products">
                Browse Our Products
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6" asChild>
              <Link href="/contact">
                Visit Our Store
                <MapPin className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>

          {/* Quick Contact Info */}
          {storeInfo && (
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center text-sm text-muted-foreground pt-8">
              {storeInfo.phone && (
                <a
                  href={`tel:${storeInfo.phone}`}
                  className="flex items-center space-x-2 hover:text-primary transition-colors"
                >
                  <Phone className="h-4 w-4" />
                  <span>{formatPhoneNumber(storeInfo.phone)}</span>
                </a>
              )}
              {storeInfo.address && (
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4" />
                  <span>
                    {storeInfo.address.city}, {storeInfo.address.state}
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Mission Statement */}
          {storeInfo?.mission && (
            <div className="max-w-2xl mx-auto pt-8">
              <blockquote className="text-lg italic text-muted-foreground border-l-4 border-primary pl-6">
                &quot;{storeInfo.mission}&quot;
              </blockquote>
            </div>
          )}
        </div>
      </div>

      {/* Background Decoration */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl" />
      </div>
    </section>
  );
}
