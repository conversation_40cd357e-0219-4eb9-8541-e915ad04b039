'use client';

import { Clock, Phone, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useStoreInfo } from '@/hooks/use-store-info';
import { isStoreOpen, getNextOpeningTime } from '@/lib/utils';

const daysOfWeek = [
  'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
];

export function StoreHours() {
  const { storeInfo } = useStoreInfo();
  const isStoreCurrentlyOpen = storeInfo?.hours ? isStoreOpen(storeInfo.hours) : false;
  const nextOpeningTime = storeInfo?.hours ? getNextOpeningTime(storeInfo.hours) : null;

  const getCurrentDay = () => {
    const today = new Date();
    return daysOfWeek[today.getDay() === 0 ? 6 : today.getDay() - 1]; // Adjust for Monday start
  };

  const currentDay = getCurrentDay();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-primary" />
            <span>Store Hours</span>
          </div>
          <Badge variant={isStoreCurrentlyOpen ? 'success' : 'secondary'}>
            {isStoreCurrentlyOpen ? 'Open Now' : 'Closed'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Hours List */}
        <div className="space-y-3">
          {storeInfo?.hours && storeInfo.hours.length > 0 ? (
            daysOfWeek.map((day) => {
              const dayHours = storeInfo.hours.find(
                h => h.dayOfWeek.toLowerCase() === day.toLowerCase()
              );
              const isToday = day === currentDay;
              
              return (
                <div 
                  key={day} 
                  className={`flex justify-between items-center p-2 rounded-md ${
                    isToday ? 'bg-primary/10 border border-primary/20' : ''
                  }`}
                >
                  <span className={`font-medium ${isToday ? 'text-primary' : 'text-muted-foreground'}`}>
                    {day}
                    {isToday && <span className="ml-2 text-xs">(Today)</span>}
                  </span>
                  <span className={`font-medium ${isToday ? 'text-primary' : ''}`}>
                    {dayHours?.isClosed 
                      ? 'Closed' 
                      : `${dayHours?.openTime || '9:00'} - ${dayHours?.closeTime || '18:00'}`
                    }
                  </span>
                </div>
              );
            })
          ) : (
            // Default hours if no store info available
            <>
              <div className="flex justify-between items-center p-2 rounded-md bg-primary/10 border border-primary/20">
                <span className="font-medium text-primary">
                  Monday - Friday
                  <span className="ml-2 text-xs">(Weekdays)</span>
                </span>
                <span className="font-medium text-primary">9:00 AM - 6:00 PM</span>
              </div>
              <div className="flex justify-between items-center p-2">
                <span className="text-muted-foreground font-medium">Saturday</span>
                <span className="font-medium">10:00 AM - 5:00 PM</span>
              </div>
              <div className="flex justify-between items-center p-2">
                <span className="text-muted-foreground font-medium">Sunday</span>
                <span className="font-medium">Closed</span>
              </div>
            </>
          )}
        </div>

        {/* Current Status */}
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <Clock className="h-4 w-4" />
            <span className="font-medium">Current Status</span>
          </div>
          
          {isStoreCurrentlyOpen ? (
            <p className="text-sm text-green-600 font-medium">
              🟢 We&apos;re open! Come visit us today.
            </p>
          ) : (
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">
                🔴 We&apos;re currently closed.
              </p>
              {nextOpeningTime && (
                <p className="text-sm text-muted-foreground">
                  Next opening: <span className="font-medium">{nextOpeningTime}</span>
                </p>
              )}
            </div>
          )}
        </div>

        {/* Special Notes */}
        <div className="space-y-3">
          <h4 className="font-medium">Important Notes</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Hours may vary during holidays</li>
            <li>• Call ahead for appointments outside regular hours</li>
            <li>• We&apos;re happy to stay late for scheduled appointments</li>
            <li>• Last customer service 30 minutes before closing</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button className="flex-1" asChild>
            <a href={`tel:${storeInfo?.phone || '******-0123'}`}>
              <Phone className="mr-2 h-4 w-4" />
              Call Store
            </a>
          </Button>
          
          <Button variant="outline" className="flex-1">
            <Calendar className="mr-2 h-4 w-4" />
            Schedule Visit
          </Button>
        </div>

        {/* Holiday Hours Notice */}
        {storeInfo?.announcements && storeInfo.announcements.some(a => a.isActive && a.type === 'info') && (
          <div className="border-l-4 border-l-blue-500 bg-blue-50 p-4 rounded-r-lg">
            <h4 className="font-medium text-blue-900 mb-1">Special Hours Notice</h4>
            {storeInfo.announcements
              .filter(a => a.isActive && a.type === 'info')
              .slice(0, 1)
              .map(announcement => (
                <p key={announcement.id} className="text-sm text-blue-800">
                  {announcement.message}
                </p>
              ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
